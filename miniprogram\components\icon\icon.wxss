/* 图标组件样式 */

.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 尺寸变体 */
.icon-container.xs {
  width: 32rpx;
  height: 32rpx;
}

.icon-container.sm {
  width: 40rpx;
  height: 40rpx;
}

.icon-container.md {
  width: 48rpx;
  height: 48rpx;
}

.icon-container.lg {
  width: 64rpx;
  height: 64rpx;
}

.icon-container.xl {
  width: 80rpx;
  height: 80rpx;
}

.icon-container.xxl {
  width: 96rpx;
  height: 96rpx;
}

/* 颜色变体 */
.icon-container.primary {
  color: var(--primary);
}

.icon-container.secondary {
  color: var(--text-secondary);
}

.icon-container.success {
  color: var(--success);
}

.icon-container.warning {
  color: var(--warning);
}

.icon-container.error {
  color: var(--error);
}

.icon-container.info {
  color: var(--info);
}

/* SVG 图标样式 */
.icon-svg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-svg svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

/* 文本图标样式 */
.icon-text {
  font-size: inherit;
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .icon-container.lg {
    width: 56rpx;
    height: 56rpx;
  }

  .icon-container.xl {
    width: 72rpx;
    height: 72rpx;
  }

  .icon-container.xxl {
    width: 88rpx;
    height: 88rpx;
  }
}
