/* 图标组件样式 */

.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 尺寸变体 */
.icon-container.xs {
  width: 32rpx;
  height: 32rpx;
}

.icon-container.sm {
  width: 40rpx;
  height: 40rpx;
}

.icon-container.md {
  width: 48rpx;
  height: 48rpx;
}

.icon-container.lg {
  width: 64rpx;
  height: 64rpx;
}

.icon-container.xl {
  width: 80rpx;
  height: 80rpx;
}

.icon-container.xxl {
  width: 96rpx;
  height: 96rpx;
}

/* 颜色变体 */
.icon-container.primary {
  color: var(--primary);
}

.icon-container.secondary {
  color: var(--text-secondary);
}

.icon-container.success {
  color: var(--success);
}

.icon-container.warning {
  color: var(--warning);
}

.icon-container.error {
  color: var(--error);
}

.icon-container.info {
  color: var(--info);
}

.icon-container.inverse {
  color: var(--text-inverse);
}

/* 字符图标样式 */
.icon-char {
  font-size: inherit;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  /* 确保emoji在所有平台显示一致 */
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

/* 文本图标样式 */
.icon-text {
  font-size: inherit;
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .icon-container.lg {
    width: 56rpx;
    height: 56rpx;
  }

  .icon-container.xl {
    width: 72rpx;
    height: 72rpx;
  }

  .icon-container.xxl {
    width: 88rpx;
    height: 88rpx;
  }
}
