<!-- 智食派首页 - 智能仪表盘 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="flex items-center justify-center" style="height: 100vh;">
    <view class="flex flex-col items-center">
      <view class="animate-pulse w-16 h-16 bg-primary-100 rounded-full mb-4"></view>
      <view class="text-secondary text-base">加载中...</view>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 个人档案卡片 -->
    <view class="card gradient-primary mb-6">
      <view class="flex justify-between items-start mb-6">
        <view class="flex items-center">
          <view class="profile-avatar">
            <icon name="profile" size="xl" color="inverse"></icon>
          </view>
          <view class="ml-3">
            <view class="text-xl font-bold text-inverse mb-1">智食派用户</view>
            <view class="text-sm text-inverse opacity-90 flex items-center">
              <icon name="target" size="sm" color="inverse" custom-style="margin-right: 8rpx;"></icon>
              减重进行中
            </view>
          </view>
        </view>
        <view class="text-right">
          <view class="text-3xl font-bold text-inverse mb-1">{{userProfile.weight}}kg</view>
          <view class="text-xs text-inverse opacity-75">目标: {{userProfile.targetWeight}}kg</view>
        </view>
      </view>

      <!-- 关键指标 -->
      <view class="grid grid-cols-3 gap-4">
        <view class="text-center">
          <view class="text-2xl font-bold text-inverse mb-1">{{bmi}}</view>
          <view class="text-xs text-inverse opacity-75">BMI</view>
        </view>
        <view class="text-center">
          <view class="text-2xl font-bold text-inverse mb-1">{{bmr}}</view>
          <view class="text-xs text-inverse opacity-75">基础代谢</view>
        </view>
        <view class="text-center">
          <view class="text-2xl font-bold text-inverse mb-1">{{goalProgress}}%</view>
          <view class="text-xs text-inverse opacity-75">目标完成</view>
        </view>
      </view>
    </view>

    <!-- 今日热量平衡 -->
    <view class="card mb-4">
      <view class="card-header">
        <view class="card-title">
          <icon name="fire" size="lg" color="primary"></icon>
          今日热量平衡
        </view>
      </view>

      <view class="card-body">
        <!-- 热量数据展示 -->
        <view class="mb-4">
          <view class="text-sm text-secondary mb-2">已摄入热量</view>
          <view class="flex items-baseline">
            <text class="text-3xl font-bold text-primary">{{todayNutrition.calories}}</text>
            <text class="text-lg text-secondary mx-1"> / </text>
            <text class="text-lg text-secondary">{{targetCalories}}</text>
            <text class="text-sm text-secondary ml-1"> kcal</text>
          </view>
        </view>

        <!-- 进度条 -->
        <view class="progress-bar mb-3">
          <view class="progress-track">
            <view class="progress-fill progress-success" style="width: {{calorieProgress}}%"></view>
          </view>
        </view>

        <!-- 状态信息 -->
        <view class="flex justify-between items-center">
          <view class="text-sm text-secondary">剩余 {{remainingCalories}} kcal</view>
          <view class="text-sm font-medium" style="color: {{statusColor}}">
            {{remainingCalories > 200 ? '✅ 安全区间' : remainingCalories >= 0 ? '⚠️ 注意控制' : '🚫 已超标'}}
          </view>
        </view>
      </view>
    </view>

    <!-- 营养素分布 -->
    <view class="section mb-6">
      <view class="grid grid-cols-3 gap-3">
        <view class="nutrient-card carbs">
          <text class="text-3xl mb-2">🍎</text>
          <view class="text-xs text-secondary mb-1">碳水化合物</view>
          <view class="text-xl font-bold text-warning">{{nutrientRatio.carbs}}%</view>
        </view>
        <view class="nutrient-card protein">
          <text class="text-3xl mb-2">🥩</text>
          <view class="text-xs text-secondary mb-1">蛋白质</view>
          <view class="text-xl font-bold text-info">{{nutrientRatio.protein}}%</view>
        </view>
        <view class="nutrient-card fat">
          <text class="text-3xl mb-2">🥑</text>
          <view class="text-xs text-secondary mb-1">脂肪</view>
          <view class="text-xl font-bold text-success">{{nutrientRatio.fat}}%</view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="section mb-6">
      <view class="grid grid-cols-2 gap-4">
        <view class="quick-action-card hover-scale" bindtap="onTakePhoto">
          <view class="action-icon-container primary mb-3">
            <icon name="camera" size="xxl" color="primary"></icon>
          </view>
          <view class="text-base font-semibold text-primary mb-1">拍照识别</view>
          <view class="text-xs text-secondary">AI智能识别食物</view>
        </view>

        <view class="quick-action-card hover-scale" bindtap="onRecordWeight">
          <view class="action-icon-container success mb-3">
            <icon name="weight" size="xxl" color="success"></icon>
          </view>
          <view class="text-base font-semibold text-primary mb-1">记录体重</view>
          <view class="text-xs text-secondary">追踪体重变化</view>
        </view>
      </view>

      <!-- 图标测试区域 -->
      <view class="text-center mt-4">
        <view class="mb-3">
          <text class="text-sm text-secondary">图标测试：</text>
          <icon name="home" size="md" color="primary" custom-style="margin: 0 8rpx;"></icon>
          <icon name="camera" size="md" color="success" custom-style="margin: 0 8rpx;"></icon>
          <icon name="fire" size="md" color="error" custom-style="margin: 0 8rpx;"></icon>
          <icon name="star" size="md" color="warning" custom-style="margin: 0 8rpx;"></icon>
        </view>
        <button class="btn btn-sm btn-outline" bindtap="onNavToIconDemo">
          <icon name="star" size="sm" color="primary" custom-style="margin-right: 8rpx;"></icon>
          查看图标系统
        </button>
      </view>
    </view>

    <!-- 今日运动 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <icon name="sport" size="lg" color="success"></icon>
          今日运动
        </view>
        <button class="btn btn-sm btn-primary" bindtap="onRecordSport">
          <icon name="plus" size="sm" color="inverse" custom-style="margin-right: 8rpx;"></icon>
          添加运动
        </button>
      </view>

      <view class="card-body">
        <view class="stat-card">
          <view class="stat-icon" style="background: rgba(16, 185, 129, 0.1);">
            <icon name="fire" size="lg" color="success"></icon>
          </view>
          <view class="stat-content">
            <view class="stat-value">{{todaySportCalories}}</view>
            <view class="stat-label">今日消耗 (kcal)</view>
            <view class="stat-change {{todaySportCalories > 0 ? 'positive' : ''}} flex items-center">
              <icon name="{{todaySportCalories > 0 ? 'check' : 'close'}}" size="sm" color="{{todaySportCalories > 0 ? 'success' : 'secondary'}}" custom-style="margin-right: 8rpx;"></icon>
              {{todaySportCalories > 0 ? '已完成运动' : '暂无运动记录'}}
            </view>
          </view>
        </view>
      </view>
    </view>


  </view>
</view>
