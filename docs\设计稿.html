<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体重管家 - 专业体重控制管理小程序</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        'bg-primary': '#f8fafc',
                        'text-primary': '#1e293b',
                        'text-secondary': '#64748b'
                    },
                    fontFamily: {
                        'sans': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #e2e8f0, #f1f5f9);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
        .nav-item {
            transition: all 0.3s ease;
        }
        .nav-item.active {
            color: #2563eb;
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="min-h-screen py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">智能体重管家</h1>
            <p class="text-gray-600 text-lg">专业体重控制管理微信小程序设计</p>
            <div class="mt-4 flex justify-center space-x-4">
                <span class="px-3 py-1 bg-primary text-white rounded-full text-sm">科学热量平衡</span>
                <span class="px-3 py-1 bg-success text-white rounded-full text-sm">AI智能识别</span>
                <span class="px-3 py-1 bg-warning text-white rounded-full text-sm">专业数据分析</span>
            </div>
        </div>

        <!-- 手机原型展示区域 -->
        <div class="flex flex-wrap justify-center gap-8">
            
            <!-- 手机1: 智能仪表盘（首页） -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <!-- 状态栏 -->
                    <div class="status-bar text-gray-800">
                        <span>9:41</span>
                        <span>智能体重管家</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 主内容区域 -->
                    <div class="flex-1 overflow-y-auto px-4 pb-20">
                        <!-- 个人档案卡片 -->
                        <div class="gradient-bg rounded-2xl p-6 mb-4 text-white card-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-lg">小明</h3>
                                        <p class="text-sm opacity-90">减重进行中</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold">68.5kg</p>
                                    <p class="text-sm opacity-90">目标: 60kg</p>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <p class="text-2xl font-bold">23.8</p>
                                    <p class="text-xs opacity-90">BMI</p>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold">1680</p>
                                    <p class="text-xs opacity-90">基础代谢</p>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold">85%</p>
                                    <p class="text-xs opacity-90">目标完成</p>
                                </div>
                            </div>
                        </div>

                        <!-- 今日概览 -->
                        <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                            <h4 class="font-bold text-lg mb-4 flex items-center">
                                <i class="fas fa-chart-line text-primary mr-2"></i>
                                今日热量平衡
                            </h4>
                            
                            <!-- 热量进度条 -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-gray-600">已摄入</span>
                                    <span class="font-bold text-primary">1245 / 1500 kcal</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-gradient-to-r from-success to-primary h-3 rounded-full" style="width: 83%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>剩余 255 kcal</span>
                                    <span class="text-success">安全区间</span>
                                </div>
                            </div>

                            <!-- 营养素分布 -->
                            <div class="grid grid-cols-3 gap-3">
                                <div class="text-center p-3 bg-red-50 rounded-xl">
                                    <div class="w-8 h-8 bg-red-100 rounded-full mx-auto mb-2 flex items-center justify-center">
                                        <i class="fas fa-bread-slice text-red-500 text-sm"></i>
                                    </div>
                                    <p class="text-xs text-gray-600">碳水</p>
                                    <p class="font-bold text-red-500">45%</p>
                                </div>
                                <div class="text-center p-3 bg-blue-50 rounded-xl">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full mx-auto mb-2 flex items-center justify-center">
                                        <i class="fas fa-drumstick-bite text-blue-500 text-sm"></i>
                                    </div>
                                    <p class="text-xs text-gray-600">蛋白质</p>
                                    <p class="font-bold text-blue-500">30%</p>
                                </div>
                                <div class="text-center p-3 bg-yellow-50 rounded-xl">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full mx-auto mb-2 flex items-center justify-center">
                                        <i class="fas fa-tint text-yellow-500 text-sm"></i>
                                    </div>
                                    <p class="text-xs text-gray-600">脂肪</p>
                                    <p class="font-bold text-yellow-500">25%</p>
                                </div>
                            </div>
                        </div>

                        <!-- 快捷操作 -->
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <div class="bg-white rounded-2xl p-4 card-shadow text-center">
                                <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-full mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-camera text-primary text-xl"></i>
                                </div>
                                <p class="font-semibold text-gray-800">拍照识别</p>
                                <p class="text-xs text-gray-500 mt-1">AI智能识别食物</p>
                            </div>
                            <div class="bg-white rounded-2xl p-4 card-shadow text-center">
                                <div class="w-12 h-12 bg-success bg-opacity-10 rounded-full mx-auto mb-3 flex items-center justify-center">
                                    <i class="fas fa-weight text-success text-xl"></i>
                                </div>
                                <p class="font-semibold text-gray-800">记录体重</p>
                                <p class="text-xs text-gray-500 mt-1">追踪体重变化</p>
                            </div>
                        </div>

                        <!-- 今日运动 -->
                        <div class="bg-white rounded-2xl p-5 card-shadow">
                            <h4 class="font-bold text-lg mb-4 flex items-center">
                                <i class="fas fa-running text-success mr-2"></i>
                                今日运动
                            </h4>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-success bg-opacity-10 rounded-full flex items-center justify-center">
                                        <i class="fas fa-fire text-success"></i>
                                    </div>
                                    <div>
                                        <p class="font-semibold">消耗 320 kcal</p>
                                        <p class="text-sm text-gray-500">跑步 30分钟</p>
                                    </div>
                                </div>
                                <button class="px-4 py-2 bg-success text-white rounded-xl text-sm">
                                    添加运动
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-2">
                        <div class="flex justify-around items-center">
                            <div class="nav-item active text-center py-2">
                                <i class="fas fa-home text-xl mb-1"></i>
                                <p class="text-xs">首页</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-utensils text-xl mb-1"></i>
                                <p class="text-xs">饮食</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-chart-bar text-xl mb-1"></i>
                                <p class="text-xs">报告</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-user text-xl mb-1"></i>
                                <p class="text-xs">我的</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机2: 饮食记录页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <!-- 状态栏 -->
                    <div class="status-bar text-gray-800">
                        <span>9:41</span>
                        <span>饮食记录</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
                        <i class="fas fa-arrow-left text-xl text-gray-600"></i>
                        <h2 class="font-bold text-lg">今日饮食</h2>
                        <i class="fas fa-calendar-alt text-xl text-primary"></i>
                    </div>

                    <!-- 主内容 -->
                    <div class="flex-1 overflow-y-auto px-4 pb-20">
                        <!-- 日期选择 -->
                        <div class="flex items-center justify-center py-4">
                            <div class="flex items-center space-x-4">
                                <i class="fas fa-chevron-left text-gray-400"></i>
                                <div class="text-center">
                                    <p class="font-bold text-lg">今天</p>
                                    <p class="text-sm text-gray-500">8月6日 周三</p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>

                        <!-- 热量总览卡片 -->
                        <div class="bg-gradient-to-r from-primary to-purple-600 rounded-2xl p-5 mb-6 text-white card-shadow">
                            <div class="text-center mb-4">
                                <p class="text-3xl font-bold">1245</p>
                                <p class="text-sm opacity-90">已摄入热量 (kcal)</p>
                            </div>
                            <div class="flex justify-between text-center">
                                <div>
                                    <p class="text-lg font-bold">255</p>
                                    <p class="text-xs opacity-90">剩余</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold">1500</p>
                                    <p class="text-xs opacity-90">目标</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold">320</p>
                                    <p class="text-xs opacity-90">运动消耗</p>
                                </div>
                            </div>
                        </div>

                        <!-- 餐次记录 -->
                        <div class="space-y-4">
                            <!-- 早餐 -->
                            <div class="bg-white rounded-2xl p-4 card-shadow">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sun text-yellow-500"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-bold">早餐</h4>
                                            <p class="text-sm text-gray-500">420 kcal</p>
                                        </div>
                                    </div>
                                    <button class="text-primary text-sm">添加</button>
                                </div>
                                
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="燕麦粥">
                                            <div>
                                                <p class="font-medium text-sm">燕麦粥</p>
                                                <p class="text-xs text-gray-500">1碗 (200g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">180 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1569288052389-dac9b01ac5b9?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="香蕉">
                                            <div>
                                                <p class="font-medium text-sm">香蕉</p>
                                                <p class="text-xs text-gray-500">1根 (120g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">105 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1550583724-b2692b85b150?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="牛奶">
                                            <div>
                                                <p class="font-medium text-sm">低脂牛奶</p>
                                                <p class="text-xs text-gray-500">1杯 (250ml)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">135 kcal</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 午餐 -->
                            <div class="bg-white rounded-2xl p-4 card-shadow">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sun text-orange-500"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-bold">午餐</h4>
                                            <p class="text-sm text-gray-500">580 kcal</p>
                                        </div>
                                    </div>
                                    <button class="text-primary text-sm">添加</button>
                                </div>
                                
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1512058564366-18510be2db19?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="鸡胸肉">
                                            <div>
                                                <p class="font-medium text-sm">鸡胸肉</p>
                                                <p class="text-xs text-gray-500">150g</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">248 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1586201375761-83865001e31c?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="糙米饭">
                                            <div>
                                                <p class="font-medium text-sm">糙米饭</p>
                                                <p class="text-xs text-gray-500">1碗 (150g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">216 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="蔬菜沙拉">
                                            <div>
                                                <p class="font-medium text-sm">蔬菜沙拉</p>
                                                <p class="text-xs text-gray-500">1份 (200g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">116 kcal</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 晚餐 -->
                            <div class="bg-white rounded-2xl p-4 card-shadow">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-moon text-purple-500"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-bold">晚餐</h4>
                                            <p class="text-sm text-gray-500">245 kcal</p>
                                        </div>
                                    </div>
                                    <button class="text-primary text-sm">添加</button>
                                </div>
                                
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1547592180-85f173990554?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="蔬菜汤">
                                            <div>
                                                <p class="font-medium text-sm">蔬菜汤</p>
                                                <p class="text-xs text-gray-500">1碗 (300ml)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">85 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="苹果">
                                            <div>
                                                <p class="font-medium text-sm">苹果</p>
                                                <p class="text-xs text-gray-500">1个 (200g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">104 kcal</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=40&h=40&fit=crop&crop=center" 
                                                 class="w-8 h-8 rounded-lg object-cover" alt="酸奶">
                                            <div>
                                                <p class="font-medium text-sm">希腊酸奶</p>
                                                <p class="text-xs text-gray-500">1杯 (100g)</p>
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium">56 kcal</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快捷添加按钮 -->
                        <div class="fixed bottom-24 right-6">
                            <button class="w-14 h-14 bg-primary rounded-full shadow-lg flex items-center justify-center animate-float">
                                <i class="fas fa-plus text-white text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-2">
                        <div class="flex justify-around items-center">
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-home text-xl mb-1"></i>
                                <p class="text-xs">首页</p>
                            </div>
                            <div class="nav-item active text-center py-2">
                                <i class="fas fa-utensils text-xl mb-1"></i>
                                <p class="text-xs">饮食</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-chart-bar text-xl mb-1"></i>
                                <p class="text-xs">报告</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-user text-xl mb-1"></i>
                                <p class="text-xs">我的</p>
                            </div>
                        </div>
                    </div>
                </div>  
            </div>

            <!-- 手机3: AI拍照识别页面 -->
            <div class="phone-frame">
                <div class="phone-screen bg-black">
                    <!-- 状态栏 -->
                    <div class="status-bar text-white">
                        <span>9:41</span>
                        <span>AI拍照识别</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
```html
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 相机界面 -->
                    <div class="relative flex-1">
                        <!-- 相机预览区域 -->
                        <div class="absolute inset-0 bg-gradient-to-b from-gray-900 to-gray-800 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=600&fit=crop&crop=center" 
                                 class="w-full h-full object-cover" alt="食物拍照">
                            
                            <!-- 识别框 -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="w-64 h-64 border-2 border-white border-dashed rounded-2xl bg-black bg-opacity-20 flex items-center justify-center">
                                    <div class="text-center text-white">
                                        <i class="fas fa-camera text-4xl mb-2"></i>
                                        <p class="text-sm">将食物放入框内</p>
                                        <p class="text-xs opacity-75">AI智能识别</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 顶部工具栏 -->
                            <div class="absolute top-16 left-0 right-0 flex justify-between items-center px-6">
                                <button class="w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                    <i class="fas fa-times text-white"></i>
                                </button>
                                <div class="flex space-x-4">
                                    <button class="w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                        <i class="fas fa-flash text-white"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                        <i class="fas fa-sync-alt text-white"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 底部控制区 -->
                            <div class="absolute bottom-8 left-0 right-0 px-6">
                                <div class="flex items-center justify-between">
                                    <!-- 相册 -->
                                    <button class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-images text-white"></i>
                                    </button>

                                    <!-- 拍照按钮 -->
                                    <button class="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg">
                                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                                            <i class="fas fa-camera text-white text-xl"></i>
                                        </div>
                                    </button>

                                    <!-- 语音识别 -->
                                    <button class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-microphone text-white"></i>
                                    </button>
                                </div>

                                <!-- 提示文字 -->
                                <div class="text-center mt-4">
                                    <p class="text-white text-sm">轻触拍照，AI智能识别食物热量</p>
                                    <p class="text-white text-xs opacity-75 mt-1">支持语音描述食物</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机4: AI识别结果页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <!-- 状态栏 -->
                    <div class="status-bar text-gray-800">
                        <span>9:41</span>
                        <span>识别结果</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
                        <i class="fas fa-arrow-left text-xl text-gray-600"></i>
                        <h2 class="font-bold text-lg">AI识别结果</h2>
                        <i class="fas fa-check text-xl text-success"></i>
                    </div>

                    <!-- 主内容 -->
                    <div class="flex-1 overflow-y-auto px-4 pb-20">
                        <!-- 识别成功提示 -->
                        <div class="text-center py-6">
                            <div class="w-16 h-16 bg-success bg-opacity-10 rounded-full mx-auto mb-4 flex items-center justify-center">
                                <i class="fas fa-check-circle text-success text-2xl"></i>
                            </div>
                            <h3 class="font-bold text-xl mb-2">识别成功！</h3>
                            <p class="text-gray-600">AI已智能识别您的食物</p>
                        </div>

                        <!-- 识别的食物卡片 -->
                        <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                            <div class="flex items-center space-x-4 mb-4">
                                <img src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=80&h=80&fit=crop&crop=center" 
                                     class="w-20 h-20 rounded-xl object-cover" alt="沙拉">
                                <div class="flex-1">
                                    <h4 class="font-bold text-lg">蔬菜沙拉</h4>
                                    <p class="text-gray-600">混合绿叶蔬菜沙拉</p>
                                    <div class="flex items-center mt-2">
                                        <div class="flex text-yellow-400">
                                            <i class="fas fa-star text-sm"></i>
                                            <i class="fas fa-star text-sm"></i>
                                            <i class="fas fa-star text-sm"></i>
                                            <i class="fas fa-star text-sm"></i>
                                            <i class="fas fa-star text-sm"></i>
                                        </div>
                                        <span class="text-sm text-success ml-2 font-medium">减肥推荐</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 营养信息 -->
                            <div class="bg-gray-50 rounded-xl p-4 mb-4">
                                <h5 class="font-bold mb-3">营养成分 (每100g)</h5>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-primary">58</p>
                                        <p class="text-sm text-gray-600">热量 (kcal)</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-green-500">2.9g</p>
                                        <p class="text-sm text-gray-600">蛋白质</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-yellow-500">11.2g</p>
                                        <p class="text-sm text-gray-600">碳水化合物</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-red-500">0.3g</p>
                                        <p class="text-sm text-gray-600">脂肪</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 分量调整 -->
                            <div class="mb-4">
                                <h5 class="font-bold mb-3">调整分量</h5>
                                <div class="flex items-center justify-between bg-gray-50 rounded-xl p-4">
                                    <button class="w-10 h-10 bg-white rounded-full shadow flex items-center justify-center">
                                        <i class="fas fa-minus text-gray-600"></i>
                                    </button>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold">200g</p>
                                        <p class="text-sm text-gray-600">约1份</p>
                                    </div>
                                    <button class="w-10 h-10 bg-white rounded-full shadow flex items-center justify-center">
                                        <i class="fas fa-plus text-primary"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 计算结果 -->
                            <div class="bg-gradient-to-r from-primary to-purple-600 rounded-xl p-4 text-white text-center">
                                <p class="text-3xl font-bold mb-1">116 kcal</p>
                                <p class="text-sm opacity-90">总热量</p>
                            </div>
                        </div>

                        <!-- 智能建议 -->
                        <div class="bg-white rounded-2xl p-5 mb-6 card-shadow">
                            <h4 class="font-bold text-lg mb-4 flex items-center">
                                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                智能建议
                            </h4>
                            <div class="space-y-3">
                                <div class="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                                    <i class="fas fa-check-circle text-success mt-1"></i>
                                    <div>
                                        <p class="font-medium text-sm">低热量高纤维</p>
                                        <p class="text-xs text-gray-600">这道沙拉热量低，纤维含量高，非常适合减肥期间食用</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                                    <i class="fas fa-info-circle text-primary mt-1"></i>
                                    <div>
                                        <p class="font-medium text-sm">建议搭配</p>
                                        <p class="text-xs text-gray-600">可以加入少量坚果或鸡胸肉增加蛋白质含量</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 添加到记录按钮 -->
                        <div class="space-y-3">
                            <button class="w-full bg-primary text-white py-4 rounded-xl font-bold text-lg">
                                添加到今日饮食
                            </button>
                            <button class="w-full border-2 border-gray-200 text-gray-700 py-4 rounded-xl font-bold">
                                重新拍照
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机5: 数据报告页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <!-- 状态栏 -->
                    <div class="status-bar text-gray-800">
                        <span>9:41</span>
                        <span>数据报告</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
                        <h2 class="font-bold text-lg">数据报告</h2>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 bg-primary text-white rounded-full text-sm">周</button>
                            <button class="px-3 py-1 text-gray-600 text-sm">月</button>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="flex-1 overflow-y-auto px-4 pb-20">
                        <!-- 体重趋势卡片 -->
                        <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-bold text-lg">体重趋势</h4>
                                <span class="text-sm text-success">↓ 2.5kg</span>
                            </div>
                            
                            <!-- 模拟图表区域 -->
                            <div class="h-40 bg-gradient-to-t from-blue-50 to-transparent rounded-lg mb-4 relative">
                                <canvas id="weightChart" class="w-full h-full"></canvas>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <p class="text-lg font-bold text-primary">68.5kg</p>
                                    <p class="text-xs text-gray-600">当前体重</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold text-success">-0.5kg</p>
                                    <p class="text-xs text-gray-600">本周变化</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold text-warning">1.5kg</p>
                                    <p class="text-xs text-gray-600">距离目标</p>
                                </div>
                            </div>
                        </div>

                        <!-- 热量平衡分析 -->
                        <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                            <h4 class="font-bold text-lg mb-4">热量平衡分析</h4>
                            
                            <!-- 热量对比图 -->
                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm">平均摄入</span>
                                    <span class="font-bold">1420 kcal</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm">平均消耗</span>
                                    <span class="font-bold">1680 kcal</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>

                            <div class="bg-green-50 rounded-lg p-3 text-center">
                                <p class="font-bold text-green-600">平均热量缺口: 260 kcal/天</p>
                                <p class="text-sm text-gray-600 mt-1">预计每周减重 0.3kg</p>
                            </div>
                        </div>

                        <!-- 营养素分析 -->
                        <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                            <h4 class="font-bold text-lg mb-4">营养素分析</h4>
                            
                            <div class="grid grid-cols-3 gap-4 mb-4">
                                <div class="text-center">
                                    <div class="w-16 h-16 mx-auto mb-2 relative">
                                        <svg class="w-16 h-16 transform -rotate-90">
                                            <circle cx="32" cy="32" r="28" stroke="#fee2e2" stroke-width="8" fill="none"/>
                                            <circle cx="32" cy="32" r="28" stroke="#ef4444" stroke-width="8" fill="none" 
                                                    stroke-dasharray="175" stroke-dashoffset="70" stroke-linecap="round"/>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-sm font-bold">45%</span>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium">碳水化合物</p>
                                    <p class="text-xs text-gray-500">建议45-60%</p>
                                </div>
                                
                                <div class="text-center">
                                    <div class="w-16 h-16 mx-auto mb-2 relative">
                                        <svg class="w-16 h-16 transform -rotate-90">
                                            <circle cx="32" cy="32" r="28" stroke="#dbeafe" stroke-width="8" fill="none"/>
                                            <circle cx="32" cy="32" r="28" stroke="#3b82f6" stroke-width="8" fill="none" 
                                                    stroke-dasharray="175" stroke-dashoffset="122.5" stroke-linecap="round"/>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-sm font-bold">30%</span>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium">蛋白质</p>
                                    <p class="text-xs text-gray-500">建议15-25%</p>
                                </div>
                                
                                <div class="text-center">
                                    <div class="w-16 h-16 mx-auto mb-2 relative">
                                        <svg class="w-16 h-16 transform -rotate-90">
                                            <circle cx="32" cy="32" r="28" stroke="#fef3c7" stroke-width="8" fill="none"/>
                                            <circle cx="32" cy="32" r="28" stroke="#f59e0b" stroke-width="8" fill="none" 
                                                    stroke-dasharray="175" stroke-dashoffset="131.25" stroke-linecap="round"/>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-sm font-bold">25%</span>
                                        </div>
                                    </div>
                                    <p class="text-sm font-medium">脂肪</p>
                                    <p class="text-xs text-gray-500">建议20-35%</p>
                                </div>
                            </div>

                            <div class="bg-blue-50 rounded-lg p-3">
                                <p class="text-sm font-medium text-blue-800">营养评价: 优秀</p>
                                <p class="text-xs text-blue-600 mt-1">蛋白质摄入充足，有助于维持肌肉量</p>
                            </div>
                        </div>

                        <!-- 减肥进度预测 -->
                        <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                            <h4 class="font-bold text-lg mb-4 flex items-center">
                                <i class="fas fa-chart-line text-primary mr-2"></i>
                                减肥进度预测
                            </h4>
                            
                            <div class="bg-gradient-to-r from-primary to-purple-600 rounded-xl p-4 text-white mb-4">
                                <div class="text-center">
                                    <p class="text-2xl font-bold mb-1">12天</p>
                                    <p class="text-sm opacity-90">预计达成目标时间</p>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-success rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">已完成 85%</span>
                                    </div>
                                    <span class="text-success font-bold">2.5kg ↓</span>
                                </div>
                                
                                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                            <i class="fas fa-target text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">剩余目标</span>
                                    </div>
                                    <span class="text-primary font-bold">1.5kg</span>
                                </div>
                            </div>
                        </div>

                        <!-- 习惯分析 -->
                        <div class="bg-white rounded-2xl p-5 card-shadow">
                            <h4 class="font-bold text-lg mb-4">习惯分析</h4>
                            
                            <div class="space-y-4">
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm">记录频率</span>
                                        <span class="font-bold text-success">95%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-success h-2 rounded-full" style="width: 95%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm">运动坚持</span>
                                        <span class="font-bold text-warning">72%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-warning h-2 rounded-full" style="width: 72%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm">目标达成</span>
                                        <span class="font-bold text-primary">88%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-primary h-2 rounded-full" style="width: 88%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 p-3 bg-yellow-50 rounded-lg">
                                <p class="text-sm font-medium text-yellow-800">建议</p>
                                <p class="text-xs text-yellow-700 mt-1">增加运动频率可以加速减重进程</p>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-2">
                        <div class="flex justify-around items-center">
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-home text-xl mb-1"></i>
                                <p class="text-xs">首页</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-utensils text-xl mb-1"></i>
                                <p class="text-xs">饮食</p>
                            </div>
                            <div class="nav-item active text-center py-2">
                                <i class="fas fa-chart-bar text-xl mb-1"></i>
                                <p class="text-xs">报告</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-user text-xl mb-1"></i>
                                <p class="text-xs">我的</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机6: 个人中心页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <!-- 状态栏 -->
                    <div class="status-bar text-gray-800">
                        <span>9:41</span>
                        <span>个人中心</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="flex-1 overflow-y-auto pb-20">
                        <!-- 个人信息卡片 -->
                        <div class="gradient-bg p-6 text-white">
                            <div class="flex items-center space-x-4 mb-6">
                                <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" 
                                         class="w-16 h-16 rounded-full object-cover" alt="用户头像">
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-xl mb-1">小明</h3>
                                    <p class="opacity-90 mb-2">减重达人 · 已坚持 45 天</p>
                                    <div class="flex items-center space-x-4 text-sm">
                                        <span>25岁</span>
                                        <span>175cm</span>
                                        <span>68.5kg</span>
                                    </div>
                                </div>
                                <i class="fas fa-edit text-xl opacity-75"></i>
                            </div>

                            <!-- 成就展示 -->
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-center">
                                    <p class="text-2xl font-bold">2.5kg</p>
                                    <p class="text-xs opacity-90">已减重</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-2xl font-bold">45</p>
                                    <p class="text-xs opacity-90">坚持天数</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-2xl font-bold">95%</p>
                                    <p class="text-xs opacity-90">记录率</p>
                                </div>
                            </div>
                        </div>

                        <!-- 我的目标 -->
                        <div class="px-4 py-4">
                            <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                                <h4 class="font-bold text-lg mb-4 flex items-center">
                                    <i class="fas fa-target text-primary mr-2"></i>
                                    我的目标
                                </h4>
                                
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium">目标体重</p>
                                            <p class="text-sm text-gray-600">健康减重目标</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-primary">60.0 kg</p>
                                            <p class="text-xs text-gray-500">剩余 8.5kg</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium">每日热量目标</p>
                                            <p class="text-sm text-gray-600">基于科学计算</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-success">1500 kcal</p>
                                            <p class="text-xs text-gray-500">预计减重0.5kg/周</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium">目标完成时间</p>
                                            <p class="text-sm text-gray-600">预计达成日期</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-warning">2024年9月15日</p>
                                            <p class="text-xs text-gray-500">还有12天</p>
                                        </div>
                                    </div>
                                </div>

                                <button class="w-full mt-4 bg-primary text-white py-3 rounded-xl font-medium">
                                    调整目标
                                </button>
                            </div>

                            <!-- 功能菜单 -->
                            <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                                <h4 class="font-bold text-lg mb-4">功能设置</h4>
                                
                                <div class="space-y-1">
                                    <div class="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-bell text-blue-500"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">提醒设置</p>
                                                <p class="text-sm text-gray-500">记录提醒、目标提醒</p>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-database text-green-500"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">数据导出</p>
                                                <p class="text-sm text-gray-500">导出体重和饮食数据</p>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-shield-alt text-purple-500"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">隐私设置</p>
                                                <p class="text-sm text-gray-500">数据安全与隐私保护</p>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-question-circle text-yellow-500"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">帮助中心</p>
                                                <p class="text-sm text-gray-500">使用指南和常见问题</p>
                                            </div>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- 健康档案 -->
                            <div class="bg-white rounded-2xl p-5 mb-4 card-shadow">
                                <h4 class="font-bold text-lg mb-4 flex items-center">
                                    <i class="fas fa-heartbeat text-red-500 mr-2"></i>
                                    健康档案
                                </h4>
                                
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="text-center p-3 bg-blue-50 rounded-xl">
                                        <p class="text-2xl font-bold text-blue-600">23.8</p>
                                        <p class="text-sm text-gray-600">BMI指数</p>
                                        <p class="text-xs text-success">正常范围</p>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded-xl">
                                        <p class="text-2xl font-bold text-green-600">1680</p>
                                        <p class="text-sm text-gray-600">基础代谢</p>
                                        <p class="text-xs text-gray-500">kcal/天</p>
                                    </div>
                                    <div class="text-center p-3 bg-purple-50 rounded-xl">
                                        <p class="text-2xl font-bold text-purple-600">18%</p>
                                        <p class="text-sm text-gray-600">体脂率</p>
                                        <p class="text-xs text-success">健康水平</p>
                                    </div>
                                    <div class="text-center p-3 bg-orange-50 rounded-xl">
                                        <p class="text-2xl font-bold text-orange-600">中等</p>
                                        <p class="text-sm text-gray-600">活动水平</p>
                                        <p class="text-xs text-gray-500">1.5倍系数</p>
                                    </div>
                                </div>

                                <button class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium">
                                    更新健康档案
                                </button>
                            </div>

                            <!-- 版本信息 -->
                            <div class="bg-white rounded-2xl p-5 card-shadow">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <i class="fas fa-weight text-primary text-2xl"></i>
                                    </div>
                                    <h4 class="font-bold text-lg mb-2">智能体重管家</h4>
                                    <p class="text-gray-600 mb-4">科学减重，智能管理</p>
                                    
                                    <div class="flex justify-center space-x-6 text-sm text-gray-500 mb-4">
                                        <span>版本 1.2.0</span>
                                        <span>|</span>
                                        <span>用户协议</span>
                                        <span>|</span>
                                        <span>隐私政策</span>
                                    </div>

                                    <button class="px-6 py-2 bg-primary text-white rounded-full text-sm">
                                        联系客服
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-2">
                        <div class="flex justify-around items-center">
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-home text-xl mb-1"></i>
                                <p class="text-xs">首页</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-utensils text-xl mb-1"></i>
                                <p class="text-xs">饮食</p>
                            </div>
                            <div class="nav-item text-center py-2 text-gray-500">
                                <i class="fas fa-chart-bar text-xl mb-1"></i>
                                <p class="text-xs">报告</p>
                            </div>
                            <div class="nav-item active text-center py-2">
                                <i class="fas fa-user text-xl mb-1"></i>
                                <p class="text-xs">我的</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 设计说明 -->
        <div class="max-w-6xl mx-auto mt-12 px-4">
            <div class="bg-white rounded-2xl p-8 card-shadow">
                <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">设计说明</h2>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold text-primary mb-4">核心特色</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle text-success mt-1"></i>
                                <span><strong>科学热量平衡:</strong> 基于BMR和TDEE的精确计算</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle text-success mt-1"></i>
                                <span><strong>AI智能识别:</strong> 拍照即可识别食物热量</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle text-success mt-1"></i>
                                <span><strong>专业数据分析:</strong> 多维度体重趋势分析</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-check-circle text-success mt-1"></i>
                                <span><strong>个性化建议:</strong> 基于用户数据的智能推荐</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-bold text-primary mb-4">设计理念</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-palette text-warning mt-1"></i>
                                <span><strong>现代简约:</strong> 清晰的信息层次和舒适的视觉体验</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-chart-line text-primary mt-1"></i>
                                <span><strong>数据可视化:</strong> 直观的图表展示减重进展</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-mobile-alt text-success mt-1"></i>
                                <span><strong>移动优先:</strong> 专为微信小程序优化的交互设计</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <i class="fas fa-heart text-danger mt-1"></i>
                                <span><strong>用户体验:</strong> 让体重管理变得简单愉悦</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="mt-8 p-6 bg-gradient-to-r from-primary to-purple-600 rounded-xl text-white text-center">
                    <h3 class="text-xl font-bold mb-2">智能体重管家</h3>
                    <p class="opacity-90">让科学的体重控制触手可及，用数据驱动健康生活</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 体重趋势图表
            const ctx = document.getElementById('weightChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['8/1', '8/2', '8/3', '8/4', '8/5', '8/6', '8/7'],
                        datasets: [{
                            label: '体重',
                            data: [71, 70.8, 70.5, 70.2, 69.8, 69.5, 68.5],
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#2563eb',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 68,
                                max: 72,
                                grid: {
                                    color: 'rgba(0,0,0,0.05)'
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#64748b'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#64748b'
                                }
                            }
                        }
                    }
                });
            }
        });

        // 添加一些交互效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活跃状态
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active');
                });
                // 添加当前活跃状态
                this.classList.add('active');
            });
        });

        // 添加按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
