// 智食派首页 - 智能仪表盘
const app = getApp();
const { userAPI, foodAPI, sportAPI, showLoading, hideLoading, showError } = require('../../utils/api');
const { calculateBMI, getBMIStatus, calculateBMR, calculateTDEE, calculateWeightLossCalories, getCalorieStatusColor, getTodayDate } = require('../../utils/calculator');
const { MEAL_NAMES, MEAL_ICONS, COLORS } = require('../../utils/constants');

Page({
  data: {
    // 用户档案数据
    userProfile: null,
    bmi: 0,
    bmiStatus: {},
    bmr: 0,
    tdee: 0,
    targetCalories: 0,
    
    // 今日数据
    todayDate: '',
    todayNutrition: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    },
    todaySportCalories: 0,
    remainingCalories: 0,
    calorieProgress: 0,
    statusColor: COLORS.SUCCESS,
    
    // 营养素分布
    nutrientRatio: {
      protein: 0,
      carbs: 0,
      fat: 0
    },
    
    // 目标完成度
    goalProgress: 0,
    
    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.setData({
      todayDate: getTodayDate()
    });
    this.initData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.refreshData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 初始化数据
  async initData() {
    try {
      showLoading('加载中...');
      await this.loadUserProfile();
      await this.loadTodayData();
      this.calculateDashboardData();
    } catch (error) {
      console.error('初始化数据失败:', error);
      showError('数据加载失败');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadTodayData();
      this.calculateDashboardData();
    } catch (error) {
      console.error('刷新数据失败:', error);
      showError('刷新失败');
    }
  },

  // 加载用户档案
  async loadUserProfile() {
    try {
      const profile = await userAPI.getUserProfile();
      this.setData({ userProfile: profile });

      // 计算基础数据
      const bmi = calculateBMI(profile.weight, profile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(profile.weight, profile.height, profile.age, profile.gender);
      const tdee = calculateTDEE(bmr, profile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, profile.goals?.weightLossRate || 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    } catch (error) {
      console.error('加载用户档案失败:', error);
      // 如果没有用户档案，使用默认数据进行演示
      const defaultProfile = {
        height: 165,
        weight: 60,
        targetWeight: 55,
        age: 25,
        gender: 'female',
        activityLevel: 'light'
      };

      this.setData({ userProfile: defaultProfile });

      const bmi = calculateBMI(defaultProfile.weight, defaultProfile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(defaultProfile.weight, defaultProfile.height, defaultProfile.age, defaultProfile.gender);
      const tdee = calculateTDEE(bmr, defaultProfile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    }
  },

  // 加载今日数据
  async loadTodayData() {
    const today = this.data.todayDate;

    try {
      // 并行加载饮食和运动数据
      const [foodRecords, sportRecords] = await Promise.all([
        foodAPI.getFoodRecords(today).catch(() => []),
        sportAPI.getSportRecords(today).catch(() => [])
      ]);

      // 计算今日营养摄入
      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFat = 0;

      foodRecords.forEach(record => {
        if (record.foods && Array.isArray(record.foods)) {
          record.foods.forEach(food => {
            totalCalories += food.calories || 0;
            totalProtein += food.protein || 0;
            totalCarbs += food.carbs || 0;
            totalFat += food.fat || 0;
          });
        }
      });

      // 计算今日运动消耗
      let totalSportCalories = 0;
      sportRecords.forEach(record => {
        totalSportCalories += record.calories || 0;
      });

      this.setData({
        todayNutrition: {
          calories: totalCalories,
          protein: totalProtein,
          carbs: totalCarbs,
          fat: totalFat
        },
        todaySportCalories: totalSportCalories
      });
    } catch (error) {
      console.error('加载今日数据失败:', error);
      // 使用模拟数据进行演示
      this.setData({
        todayNutrition: {
          calories: 1200,
          protein: 60,
          carbs: 150,
          fat: 40
        },
        todaySportCalories: 300
      });
    }
  },

  // 计算仪表盘数据
  calculateDashboardData() {
    const { todayNutrition, todaySportCalories, targetCalories, userProfile } = this.data;
    
    // 计算剩余热量（考虑运动消耗）
    const netCalories = todayNutrition.calories - todaySportCalories;
    const remainingCalories = targetCalories - netCalories;
    const calorieProgress = Math.min((netCalories / targetCalories) * 100, 100);
    
    // 获取状态颜色
    const statusColor = getCalorieStatusColor(netCalories, targetCalories);
    
    // 计算营养素占比
    const totalNutrientCalories = 
      (todayNutrition.protein * 4) + 
      (todayNutrition.carbs * 4) + 
      (todayNutrition.fat * 9);
    
    let nutrientRatio = { protein: 0, carbs: 0, fat: 0 };
    if (totalNutrientCalories > 0) {
      nutrientRatio = {
        protein: Math.round(((todayNutrition.protein * 4) / totalNutrientCalories) * 100),
        carbs: Math.round(((todayNutrition.carbs * 4) / totalNutrientCalories) * 100),
        fat: Math.round(((todayNutrition.fat * 9) / totalNutrientCalories) * 100)
      };
    }
    
    // 计算目标完成度
    let goalProgress = 0;
    if (userProfile && userProfile.targetWeight) {
      const totalWeightLoss = userProfile.weight - userProfile.targetWeight;
      const currentProgress = userProfile.weight - userProfile.weight; // 这里需要获取最新体重
      goalProgress = totalWeightLoss > 0 ? (currentProgress / totalWeightLoss) * 100 : 0;
    }
    
    this.setData({
      remainingCalories,
      calorieProgress,
      statusColor,
      nutrientRatio,
      goalProgress
    });
  },

  // 快捷操作 - 拍照识别
  onTakePhoto() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    });
  },

  // 快捷操作 - 记录体重
  onRecordWeight() {
    wx.showModal({
      title: '记录体重',
      editable: true,
      placeholderText: '请输入体重(kg)',
      success: async (res) => {
        if (res.confirm && res.content) {
          const weight = parseFloat(res.content);
          if (weight > 0 && weight < 300) {
            try {
              showLoading('记录中...');
              await userAPI.addWeightRecord(weight);
              // 更新用户档案中的体重
              const updatedProfile = { ...this.data.userProfile, weight };
              this.setData({ userProfile: updatedProfile });
              this.calculateDashboardData();
              wx.showToast({
                title: '记录成功',
                icon: 'success'
              });
            } catch (error) {
              showError('记录失败');
            } finally {
              hideLoading();
            }
          } else {
            showError('请输入有效的体重');
          }
        }
      }
    });
  },

  // 快捷操作 - 记录饮食
  onRecordFood() {
    wx.navigateTo({
      url: '/pages/food/food'
    });
  },

  // 快捷操作 - 记录运动
  onRecordSport() {
    wx.navigateTo({
      url: '/pages/sport/sport'
    });
  },

  // 查看详细报告
  onViewReport() {
    wx.switchTab({
      url: '/pages/report/report'
    });
  },

  // 查看个人中心
  onViewProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  // 导航到饮食页面
  onNavToFood() {
    wx.navigateTo({
      url: '/pages/food/food'
    });
  },

  // 导航到报告页面
  onNavToReport() {
    wx.navigateTo({
      url: '/pages/report/report'
    });
  },

  // 导航到个人中心
  onNavToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  }
});
