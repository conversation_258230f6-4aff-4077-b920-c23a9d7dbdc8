# 智食派微信小程序需求文档

## 介绍

智食派是一款基于科学热量平衡原理的专业体重控制管理微信小程序，通过智能化的饮食记录、数据分析和个性化建议，帮助用户实现可持续的体重管理目标。

## 核心价值主张

- **科学性**：基于热量平衡原理（热量摄入 < 热量消耗 = 体重下降）
- **智能化**：AI食物识别、智能数据分析、个性化建议
- **专业性**：精准的营养数据、科学的减重预测、健康风险提醒

## 设计风格和配色

基于设计稿，确定以下设计规范：

### 主色调
- **主色**：#2563eb（蓝色）- 科技感、专业性
- **成功色**：#10b981（绿色）- 健康、积极
- **警告色**：#f59e0b（橙色）- 提醒、注意
- **危险色**：#ef4444（红色）- 警告、超标

### 设计风格
- **现代简约**：清爽的界面设计，突出内容
- **卡片式布局**：信息层次清晰，易于阅读
- **渐变背景**：增加视觉层次感
- **圆角设计**：友好亲和的视觉体验

## 需求

### 需求 1 - 智能仪表盘（首页）

**用户故事：** 作为用户，我希望在首页能够快速了解我的基本健康信息和今日进度，以便掌握当前的减重状态。

#### 验收标准

1. When 用户打开小程序时，系统应当显示个人档案卡片，包含身高、当前体重、目标体重、BMI值、基础代谢率和目标完成度。
2. When 用户查看今日概览时，系统应当显示今日可摄入热量、已摄入热量、剩余热量，并用颜色编码表示状态（绿色：安全区，黄色：警告区，红色：超标区）。
3. When 用户查看营养素分布时，系统应当以饼图形式显示碳水化合物、蛋白质、脂肪的摄入占比。
4. When 用户需要快速记录时，系统应当提供拍照识别、记录体重、记录饮食、记录运动的快捷入口。

### 需求 2 - 专业饮食记录

**用户故事：** 作为用户，我希望能够通过多种方式便捷地记录我的饮食，并获得准确的营养分析，以便科学管理我的热量摄入。

#### 验收标准

1. When 用户选择拍照识别时，系统应当调用微信相机API，使用AI服务识别食物类型和估算分量，并支持手动调整。
2. When 用户选择语音记录时，系统应当调用微信语音识别API，解析自然语言描述的食物信息。
3. When 用户选择搜索添加时，系统应当提供食物搜索功能，支持拼音搜索和模糊匹配，显示搜索历史。
4. When 用户选择扫码录入时，系统应当调用微信扫码API，识别包装食品条码并获取营养信息。
5. When 用户记录食物后，系统应当显示营养成分分析，包括三大营养素占比、热量密度、饱腹感指数、升糖指数。

### 需求 3 - AI拍照识别

**用户故事：** 作为用户，我希望通过拍照就能快速识别食物并获得营养信息，以便简化饮食记录过程。

#### 验收标准

1. When 用户进入拍照界面时，系统应当显示相机预览，提供识别框引导用户拍摄。
2. When 用户拍照后，系统应当使用AI服务识别食物，显示识别结果包括食物名称、营养成分、减肥友好度评级。
3. When 识别完成后，系统应当允许用户调整分量，实时计算总热量。
4. When 用户确认后，系统应当提供智能建议，如低热量高纤维提示、搭配建议等。

### 需求 4 - 体重趋势分析

**用户故事：** 作为用户，我希望能够查看我的体重变化趋势和数据分析，以便了解减重效果和调整策略。

#### 验收标准

1. When 用户查看体重趋势时，系统应当显示体重变化折线图，支持7天/30天/90天时间范围切换。
2. When 系统分析减重速度时，应当判断是否在健康范围内（0.5-1kg/周），并提供相应提醒。
3. When 用户查看热量平衡分析时，系统应当显示每日热量摄入vs消耗对比图，计算热量缺口。
4. When 系统进行习惯分析时，应当统计饮食记录频率、高热量食物摄入频次、最容易超标的时间段。

### 需求 5 - 运动管理

**用户故事：** 作为用户，我希望能够记录我的运动并获得热量消耗计算，以便平衡我的热量摄入和消耗。

#### 验收标准

1. When 用户记录运动时，系统应当提供常见运动类型数据库，支持自定义运动类型。
2. When 用户输入运动时长时，系统应当基于体重和运动强度自动计算热量消耗。
3. When 系统提供运动推荐时，应当基于当前体重计算不同运动的热量消耗，推荐适合减肥的运动类型。
4. When 用户设定运动目标时，系统应当提供每日运动目标设定和完成度追踪。

### 需求 6 - 个人中心

**用户故事：** 作为用户，我希望能够管理我的个人信息和减肥计划，以便获得个性化的服务。

#### 验收标准

1. When 用户管理基础档案时，系统应当支持录入和修改身高、体重、年龄、性别、活动水平等信息。
2. When 用户设定目标时，系统应当支持设置目标体重、期望减重速度、目标达成日期。
3. When 系统制定减肥计划时，应当基于个人数据自动计算每日热量需求，制定个性化减肥方案。
4. When 用户进行设置时，系统应当提供提醒设置、单位设置、隐私设置等个性化选项。

## 核心算法需求

### 热量需求计算
- 使用 Mifflin-St Jeor 公式计算基础代谢率（BMR）
- 根据活动水平计算每日总消耗（TDEE）
- 基于减重目标计算每日热量摄入目标

### 减重预测
- 基于热量缺口预测理论减重速度
- 计算预计达成目标时间
- 提供健康风险评估和建议

## 技术约束

1. **平台限制**：微信小程序平台，遵循微信小程序开发规范
2. **认证方式**：小程序云开发天然免登录，通过 wx-server-sdk 获取 OPENID
3. **AI服务**：集成第三方食物识别AI服务
4. **数据存储**：使用云开发数据库存储用户数据
5. **实时性**：支持实时数据更新和同步

## 非功能性需求

1. **性能**：页面加载时间不超过3秒
2. **可用性**：界面简洁直观，操作流程不超过3步
3. **兼容性**：支持微信7.0以上版本
4. **安全性**：用户数据加密存储，隐私保护
5. **可扩展性**：支持后续功能扩展和数据迁移
