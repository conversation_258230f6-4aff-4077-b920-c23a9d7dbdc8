/* 智食派AI拍照识别页面样式 */

.page-container {
  height: 100vh;
  background: #000;
}

/* 相机界面 */
.camera-container {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.camera-preview {
  flex: 1;
  width: 100%;
  position: relative;
}

/* 识别框引导 */
.camera-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.guide-frame {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
}

.guide-corner {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 6rpx solid #2563eb;
}

.guide-corner.tl {
  top: -6rpx;
  left: -6rpx;
  border-right: none;
  border-bottom: none;
  border-radius: 12rpx 0 0 0;
}

.guide-corner.tr {
  top: -6rpx;
  right: -6rpx;
  border-left: none;
  border-bottom: none;
  border-radius: 0 12rpx 0 0;
}

.guide-corner.bl {
  bottom: -6rpx;
  left: -6rpx;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 12rpx;
}

.guide-corner.br {
  bottom: -6rpx;
  right: -6rpx;
  border-left: none;
  border-top: none;
  border-radius: 0 0 12rpx 0;
}

.guide-text {
  text-align: center;
  color: white;
  font-size: 28rpx;
  margin-top: 32rpx;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

/* 相机控制栏 */
.camera-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 48rpx 32rpx;
  padding-bottom: calc(48rpx + env(safe-area-inset-bottom));
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.control-icon {
  font-size: 48rpx;
}

.control-text {
  color: white;
  font-size: 24rpx;
}

.capture-btn {
  width: 120rpx;
  height: 120rpx;
  border: 6rpx solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.capture-btn:active {
  transform: scale(0.9);
}

.capture-inner {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
}

/* 餐次指示器 */
.meal-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  padding-top: calc(32rpx + env(safe-area-inset-top));
  background: linear-gradient(rgba(0,0,0,0.8), transparent);
  color: white;
  text-align: center;
  font-size: 28rpx;
}

/* 识别结果界面 */
.result-container {
  height: 100vh;
  background: var(--bg-primary);
  overflow-y: auto;
}

/* 识别中状态 */
.recognizing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: white;
}

.recognizing-animation {
  font-size: 120rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.recognizing-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-top: 32rpx;
}

/* 识别结果 */
.recognition-result {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.result-image-container {
  position: relative;
  margin-bottom: 32rpx;
}

.result-image {
  width: 100%;
  height: 400rpx;
  border-radius: 24rpx;
  background: #f0f0f0;
}

.retake-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.retake-btn:active {
  opacity: 0.8;
}

/* 识别的食物 */
.recognized-foods {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 24rpx;
}

.no-result {
  text-align: center;
  padding: 64rpx 32rpx;
  background: white;
  border-radius: 24rpx;
}

.no-result-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.no-result-hint {
  font-size: 24rpx;
  color: var(--text-secondary);
  opacity: 0.7;
}

.food-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--shadow);
}

.food-result-main {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.food-result-info {
  flex: 1;
}

.food-result-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.food-result-amount {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.food-result-nutrition {
  text-align: right;
}

.food-result-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.food-result-macros {
  font-size: 20rpx;
  color: var(--text-secondary);
  line-height: 1.2;
}

.food-result-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-left: 24rpx;
}

.adjust-btn,
.delete-btn {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
}

.adjust-btn {
  background: var(--primary-color);
  color: white;
}

.delete-btn {
  background: var(--danger-color);
  color: white;
}

.adjust-btn:active,
.delete-btn:active {
  opacity: 0.8;
}

/* 智能建议 */
.recommendations {
  margin-bottom: 32rpx;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--shadow);
}

.recommendation-item:active {
  background: var(--bg-primary);
}

.recommendation-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.recommendation-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.recommendation-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-left: 16rpx;
}

/* 操作按钮 */
.result-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 2rpx solid var(--border-color);
  box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 24rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}

.action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.action-btn.secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.action-btn.disabled {
  background: var(--text-secondary);
  opacity: 0.5;
}

.action-btn:active:not(.disabled) {
  opacity: 0.8;
}
