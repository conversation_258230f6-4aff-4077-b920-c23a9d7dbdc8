// 饮食管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action } = event;

  try {
    switch (action) {
      case 'addFoodRecord':
        return await addFoodRecord(wxContext.OPENID, event.meal, event.foods, event.recordType, event.date);
      case 'getFoodRecords':
        return await getFoodRecords(wxContext.OPENID, event.date);
      case 'searchFood':
        return await searchFood(event.keyword);
      case 'getDailyNutrition':
        return await getDailyNutrition(wxContext.OPENID, event.date);
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { success: false, error: error.message };
  }
};

// 添加饮食记录
async function addFoodRecord(openid, meal, foods, recordType, date) {
  await db.collection('food_records').add({
    data: {
      _openid: openid,
      meal: meal,
      foods: foods,
      recordType: recordType,
      date: date,
      createdAt: new Date()
    }
  });

  return { success: true, data: { meal, foods, recordType, date } };
}

// 获取饮食记录
async function getFoodRecords(openid, date) {
  const result = await db.collection('food_records')
    .where({
      _openid: openid,
      date: date
    })
    .orderBy('createdAt', 'asc')
    .get();

  return { success: true, data: result.data };
}

// 搜索食物
async function searchFood(keyword) {
  // 这里应该从食物库中搜索，暂时返回模拟数据
  const mockFoods = [
    { name: '苹果', calories: 52, protein: 0.3, carbs: 13.8, fat: 0.2, fiber: 2.4 },
    { name: '香蕉', calories: 89, protein: 1.1, carbs: 22.8, fat: 0.3, fiber: 2.6 },
    { name: '米饭', calories: 130, protein: 2.7, carbs: 28, fat: 0.3, fiber: 0.4 },
    { name: '鸡胸肉', calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0 }
  ];

  const results = mockFoods.filter(food => 
    food.name.includes(keyword)
  );

  return { success: true, data: results };
}

// 获取每日营养分析
async function getDailyNutrition(openid, date) {
  const result = await db.collection('food_records')
    .where({
      _openid: openid,
      date: date
    })
    .get();

  let totalCalories = 0;
  let totalProtein = 0;
  let totalCarbs = 0;
  let totalFat = 0;

  result.data.forEach(record => {
    if (record.foods && Array.isArray(record.foods)) {
      record.foods.forEach(food => {
        totalCalories += food.calories || 0;
        totalProtein += food.protein || 0;
        totalCarbs += food.carbs || 0;
        totalFat += food.fat || 0;
      });
    }
  });

  return {
    success: true,
    data: {
      calories: totalCalories,
      protein: totalProtein,
      carbs: totalCarbs,
      fat: totalFat
    }
  };
}
