// 智食派AI拍照识别页面
const { aiAPI, foodAPI, uploadImage, showLoading, hideLoading, showSuccess, showError } = require('../../utils/api');
const { MEAL_NAMES, RECORD_TYPES } = require('../../utils/constants');

Page({
  data: {
    // 相机相关
    cameraContext: null,
    cameraReady: false,
    
    // 识别相关
    imageUrl: '',
    recognizing: false,
    recognitionResult: null,
    
    // 餐次信息
    targetMeal: 'breakfast',
    mealName: '',
    
    // 食物调整
    adjustedFoods: [],
    
    // 页面状态
    currentStep: 'camera', // camera, result, adjust
    
    // 智能建议
    recommendations: []
  },

  onLoad(options) {
    // 获取目标餐次
    const meal = options.meal || 'breakfast';
    this.setData({
      targetMeal: meal,
      mealName: MEAL_NAMES[meal]
    });
    
    this.initCamera();
  },

  onReady() {
    // 创建相机上下文
    this.cameraContext = wx.createCameraContext();
    this.setData({ cameraReady: true });
  },

  onUnload() {
    // 页面卸载时清理资源
    if (this.data.imageUrl) {
      // 清理临时图片
    }
  },

  // 初始化相机
  initCamera() {
    // 检查相机权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.camera']) {
          wx.authorize({
            scope: 'scope.camera',
            success: () => {
              console.log('相机权限获取成功');
            },
            fail: () => {
              wx.showModal({
                title: '需要相机权限',
                content: '请在设置中开启相机权限',
                showCancel: false,
                success: () => {
                  wx.navigateBack();
                }
              });
            }
          });
        }
      }
    });
  },

  // 拍照
  onTakePhoto() {
    if (!this.data.cameraReady) {
      showError('相机未准备就绪');
      return;
    }

    this.cameraContext.takePhoto({
      quality: 'high',
      success: (res) => {
        this.setData({ imageUrl: res.tempImagePath });
        this.recognizeFood(res.tempImagePath);
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        showError('拍照失败');
      }
    });
  },

  // 从相册选择
  onChooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({ imageUrl: tempFilePath });
        this.recognizeFood(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        showError('选择图片失败');
      }
    });
  },

  // 识别食物
  async recognizeFood(imagePath) {
    try {
      this.setData({ recognizing: true });
      showLoading('AI识别中...');

      // 上传图片到云存储
      const cloudPath = `food-images/${Date.now()}.jpg`;
      const fileID = await uploadImage(imagePath, cloudPath);

      // 调用AI识别
      const result = await aiAPI.recognizeFood(fileID);
      
      // 处理识别结果
      this.processRecognitionResult(result);
      
      // 获取智能建议
      const recommendations = await aiAPI.getRecommendations(result);
      
      this.setData({
        recognitionResult: result,
        adjustedFoods: result.foods || [],
        recommendations: recommendations || [],
        currentStep: 'result'
      });

    } catch (error) {
      console.error('食物识别失败:', error);
      showError('识别失败，请重试');
    } finally {
      this.setData({ recognizing: false });
      hideLoading();
    }
  },

  // 处理识别结果
  processRecognitionResult(result) {
    // 为每个识别的食物添加默认分量
    if (result.foods && Array.isArray(result.foods)) {
      result.foods.forEach(food => {
        if (!food.amount) {
          food.amount = 100; // 默认100g
        }
        if (!food.unit) {
          food.unit = 'g';
        }
      });
    }
  },

  // 调整食物分量
  onAdjustAmount(e) {
    const { index } = e.currentTarget.dataset;
    const food = this.data.adjustedFoods[index];
    
    wx.showModal({
      title: `调整${food.name}的分量`,
      content: `当前: ${food.amount}${food.unit}`,
      editable: true,
      placeholderText: '请输入新的分量',
      success: (res) => {
        if (res.confirm && res.content) {
          const newAmount = parseFloat(res.content);
          if (newAmount > 0) {
            this.updateFoodAmount(index, newAmount);
          } else {
            showError('请输入有效的分量');
          }
        }
      }
    });
  },

  // 更新食物分量
  updateFoodAmount(index, newAmount) {
    const adjustedFoods = [...this.data.adjustedFoods];
    const food = adjustedFoods[index];
    const originalAmount = food.originalAmount || food.amount;
    const ratio = newAmount / originalAmount;

    // 重新计算营养值
    adjustedFoods[index] = {
      ...food,
      amount: newAmount,
      calories: Math.round(food.originalCalories * ratio),
      protein: Math.round((food.originalProtein || 0) * ratio * 10) / 10,
      carbs: Math.round((food.originalCarbs || 0) * ratio * 10) / 10,
      fat: Math.round((food.originalFat || 0) * ratio * 10) / 10,
      fiber: Math.round((food.originalFiber || 0) * ratio * 10) / 10
    };

    // 保存原始值（如果还没有保存）
    if (!food.originalAmount) {
      adjustedFoods[index].originalAmount = originalAmount;
      adjustedFoods[index].originalCalories = food.calories;
      adjustedFoods[index].originalProtein = food.protein;
      adjustedFoods[index].originalCarbs = food.carbs;
      adjustedFoods[index].originalFat = food.fat;
      adjustedFoods[index].originalFiber = food.fiber;
    }

    this.setData({ adjustedFoods });
  },

  // 删除食物
  onDeleteFood(e) {
    const { index } = e.currentTarget.dataset;
    const adjustedFoods = [...this.data.adjustedFoods];
    adjustedFoods.splice(index, 1);
    this.setData({ adjustedFoods });
  },

  // 确认添加
  async onConfirmAdd() {
    const { adjustedFoods, targetMeal } = this.data;
    
    if (adjustedFoods.length === 0) {
      showError('请至少保留一个食物');
      return;
    }

    try {
      showLoading('添加中...');
      
      // 添加到饮食记录
      await foodAPI.addFoodRecord(targetMeal, adjustedFoods, RECORD_TYPES.CAMERA);
      
      showSuccess('添加成功');
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('添加食物记录失败:', error);
      showError('添加失败');
    } finally {
      hideLoading();
    }
  },

  // 重新拍照
  onRetakePhoto() {
    this.setData({
      imageUrl: '',
      recognitionResult: null,
      adjustedFoods: [],
      recommendations: [],
      currentStep: 'camera'
    });
  },

  // 查看建议详情
  onViewRecommendation(e) {
    const { recommendation } = e.currentTarget.dataset;
    wx.showModal({
      title: '智能建议',
      content: recommendation.content,
      showCancel: false
    });
  },

  // 相机错误处理
  onCameraError(e) {
    console.error('相机错误:', e.detail);
    showError('相机出现错误');
  },

  // 返回上一页
  onGoBack() {
    wx.navigateBack();
  }
});
