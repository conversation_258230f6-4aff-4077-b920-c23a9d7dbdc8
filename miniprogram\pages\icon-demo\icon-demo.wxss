/* 图标展示页面样式 */

.icon-demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-out);
}

.icon-demo-item:active {
  transform: scale(0.95);
  background: var(--gray-200);
}

.icon-name {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-top: var(--space-2);
  text-align: center;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
