/* 智食派首页样式 - 基于新设计系统 */

/* ===== 个人档案卡片 ===== */
.profile-avatar {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

/* ===== 营养素卡片 ===== */
.nutrient-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--duration-fast) var(--ease-out);
}

.nutrient-card:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-md);
}

.nutrient-card.carbs {
  background: linear-gradient(135deg, #FEF2F2, #FEE2E2);
  border-color: #FECACA;
}

.nutrient-card.protein {
  background: linear-gradient(135deg, #EFF6FF, #DBEAFE);
  border-color: #BFDBFE;
}

.nutrient-card.fat {
  background: linear-gradient(135deg, #FFFBEB, #FEF3C7);
  border-color: #FDE68A;
}

/* ===== 快捷操作卡片 ===== */
.quick-action-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--duration-fast) var(--ease-out);
}

.action-icon-container {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.action-icon-container.primary {
  background: var(--primary-50);
  border: 2rpx solid var(--primary-100);
}

.action-icon-container.success {
  background: rgba(16, 185, 129, 0.1);
  border: 2rpx solid rgba(16, 185, 129, 0.2);
}

/* 移除底部导航预留空间，使用系统tabBar */
.container {
  padding-bottom: var(--space-4);
}

/* ===== 响应式调整 ===== */
@media (max-width: 375px) {
  .profile-avatar {
    width: 80rpx;
    height: 80rpx;
  }

  .nutrient-card {
    padding: var(--space-3);
  }

  .quick-action-card {
    padding: var(--space-4);
  }

  .action-icon-container {
    width: 80rpx;
    height: 80rpx;
  }

  .nav-icon {
    font-size: var(--text-xl);
  }

  .nav-text {
    font-size: 20rpx;
  }

  .container {
    padding: var(--space-3);
    padding-bottom: 140rpx;
  }
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
  .nutrient-card.carbs {
    background: linear-gradient(135deg, #7F1D1D, #991B1B);
  }

  .nutrient-card.protein {
    background: linear-gradient(135deg, #1E3A8A, #1D4ED8);
  }

  .nutrient-card.fat {
    background: linear-gradient(135deg, #92400E, #B45309);
  }
}
