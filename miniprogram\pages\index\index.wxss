/* 智食派首页样式 */

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 32rpx;
}

/* 个人档案卡片 */
.profile-card {
  margin-bottom: 32rpx;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.profile-avatar {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 48rpx;
  color: white;
}

.profile-details {
  flex: 1;
}

.profile-name {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.profile-status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-weight {
  text-align: right;
}

.current-weight {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.target-weight {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
  text-align: center;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.metric-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 热量概览 */
.calorie-overview {
  padding: 8rpx 0;
}

.calorie-header {
  margin-bottom: 24rpx;
}

.calorie-consumed-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.calorie-main {
  display: flex;
  align-items: baseline;
}

.calorie-consumed {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.calorie-separator,
.calorie-target,
.calorie-unit {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.calorie-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  font-size: 24rpx;
}

.status-left {
  color: var(--text-secondary);
}

.status-right {
  font-weight: 600;
}

/* 营养素分布 */
.nutrient-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.nutrient-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: var(--shadow);
}

.nutrient-card.carbs {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
}

.nutrient-card.protein {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.nutrient-card.fat {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.nutrient-icon {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.nutrient-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.nutrient-percentage {
  font-size: 32rpx;
  font-weight: bold;
}

.nutrient-card.carbs .nutrient-percentage {
  color: #ef4444;
}

.nutrient-card.protein .nutrient-percentage {
  color: #3b82f6;
}

.nutrient-card.fat .nutrient-percentage {
  color: #f59e0b;
}

/* 快捷操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.quick-action-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.quick-action-card:active {
  transform: scale(0.95);
}

.action-icon-container {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}

.action-icon-container.primary {
  background: rgba(37, 99, 235, 0.1);
}

.action-icon-container.success {
  background: rgba(16, 185, 129, 0.1);
}

.action-icon {
  font-size: 48rpx;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 今日运动 */
.sport-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sport-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.sport-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sport-icon {
  font-size: 32rpx;
  color: var(--success-color);
}

.sport-details {
  flex: 1;
}

.sport-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.sport-activity {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.sport-action {
  margin-left: 24rpx;
}

.add-sport-btn {
  padding: 16rpx 32rpx;
  background: var(--success-color);
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.add-sport-btn:active {
  opacity: 0.8;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  border-top: 2rpx solid var(--border-color);
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 8rpx;
  transition: all 0.3s ease;
}

.nav-item:active {
  background: var(--bg-primary);
}

.nav-item.active .nav-icon {
  color: var(--primary-color);
}

.nav-item.active .nav-text {
  color: var(--primary-color);
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: var(--text-secondary);
}

.nav-text {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 为底部导航留出空间 */
.container {
  padding-bottom: 120rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .profile-stats {
    gap: 16rpx;
  }

  .stat-value {
    font-size: 24rpx;
  }

  .calorie-consumed {
    font-size: 40rpx;
  }

  .quick-actions {
    gap: 16rpx;
  }

  .action-item {
    padding: 24rpx;
  }

  .action-icon {
    font-size: 40rpx;
  }

  .action-name {
    font-size: 24rpx;
  }

  .nav-icon {
    font-size: 36rpx;
  }

  .nav-text {
    font-size: 18rpx;
  }
}
