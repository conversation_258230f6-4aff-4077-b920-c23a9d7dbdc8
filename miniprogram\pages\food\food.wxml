<!-- 智食派饮食记录页面 -->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <input 
        class="search-input" 
        placeholder="搜索食物..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        focus="{{showSearchResults}}"
      />
      <view class="search-icon">🔍</view>
    </view>
    
    <!-- 搜索结果 -->
    <view wx:if="{{showSearchResults}}" class="search-results">
      <view wx:if="{{searching}}" class="search-loading">搜索中...</view>
      <view wx:elif="{{searchResults.length === 0}}" class="search-empty">未找到相关食物</view>
      <view wx:else>
        <view 
          wx:for="{{searchResults}}" 
          wx:key="index"
          class="search-result-item"
          data-food="{{item}}"
          data-meal="breakfast"
          bindtap="onSelectSearchResult"
        >
          <view class="food-info">
            <view class="food-name">{{item.name}}</view>
            <view class="food-desc">{{item.calories}}kcal/100g</view>
          </view>
          <view class="add-btn">+</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日总计 -->
  <view class="card total-card">
    <view class="card-title">
      <text class="icon">📊</text>
      今日总计
    </view>
    <view class="total-stats">
      <view class="total-item">
        <view class="total-value">{{todayTotal.calories}}</view>
        <view class="total-label">热量(kcal)</view>
      </view>
      <view class="total-item">
        <view class="total-value">{{todayTotal.protein}}</view>
        <view class="total-label">蛋白质(g)</view>
      </view>
      <view class="total-item">
        <view class="total-value">{{todayTotal.carbs}}</view>
        <view class="total-label">碳水(g)</view>
      </view>
      <view class="total-item">
        <view class="total-value">{{todayTotal.fat}}</view>
        <view class="total-label">脂肪(g)</view>
      </view>
    </view>
  </view>

  <!-- 餐次列表 -->
  <view class="meals-container">
    <view wx:for="{{['breakfast', 'lunch', 'dinner', 'snack']}}" wx:key="*this" class="meal-section">
      <view class="meal-header">
        <view class="meal-info">
          <text class="meal-icon">{{meals[item].icon}}</text>
          <text class="meal-name">{{meals[item].name}}</text>
          <text class="meal-calories">{{meals[item].totalCalories}}kcal</text>
        </view>
        
        <!-- 快捷添加按钮 -->
        <view class="quick-add-buttons">
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onTakePhoto"
          >
            📷
          </view>
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onVoiceRecord"
          >
            🎤
          </view>
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onScanBarcode"
          >
            📱
          </view>
        </view>
      </view>

      <!-- 食物列表 -->
      <view class="food-list">
        <view wx:if="{{meals[item].foods.length === 0}}" class="empty-meal">
          <view class="empty-text">暂无记录</view>
          <view class="empty-hint">点击上方按钮添加食物</view>
        </view>
        
        <view wx:else>
          <view 
            wx:for="{{meals[item].foods}}" 
            wx:for-item="food"
            wx:for-index="foodIndex"
            wx:key="foodIndex"
            class="food-item"
          >
            <view class="food-main">
              <view class="food-details">
                <view class="food-name">{{food.name}}</view>
                <view class="food-amount">{{food.amount}}{{food.unit}}</view>
              </view>
              <view class="food-nutrition">
                <view class="food-calories">{{food.calories}}kcal</view>
                <view class="food-macros">
                  蛋白质{{food.protein}}g | 碳水{{food.carbs}}g | 脂肪{{food.fat}}g
                </view>
              </view>
            </view>
            
            <view class="food-actions">
              <view class="record-type">{{food.recordType === 'camera' ? '📷' : '✏️'}}</view>
              <view 
                class="delete-btn"
                data-meal="{{item}}"
                data-index="{{foodIndex}}"
                bindtap="onDeleteFood"
              >
                🗑️
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-btn" bindtap="onViewNutrition">
      <view class="action-icon">📈</view>
      <view class="action-text">营养分析</view>
    </view>
  </view>
</view>
