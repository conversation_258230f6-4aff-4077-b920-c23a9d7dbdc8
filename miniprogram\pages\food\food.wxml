<!-- 智食派饮食记录页面 -->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-container mb-4">
    <view class="input-group">
      <view class="relative">
        <input
          class="input-field pl-12"
          placeholder="搜索食物..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          focus="{{showSearchResults}}"
        />
        <icon name="🔍" size="md" color="secondary" custom-style="position: absolute; left: 12rpx; top: 50%; transform: translateY(-50%);"></icon>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view wx:if="{{showSearchResults}}" class="search-results card mt-2 p-0">
      <view wx:if="{{searching}}" class="flex items-center justify-center py-8">
        <view class="animate-pulse text-secondary">搜索中...</view>
      </view>
      <view wx:elif="{{searchResults.length === 0}}" class="flex flex-col items-center justify-center py-8">
        <icon name="🔍" size="xl" color="secondary" custom-style="opacity: 0.5; mb-2"></icon>
        <view class="text-secondary text-sm">未找到相关食物</view>
      </view>
      <view wx:else>
        <view
          wx:for="{{searchResults}}"
          wx:key="index"
          class="search-result-item hover-bg p-4 flex justify-between items-center border-b border-gray-100 last:border-b-0"
          data-food="{{item}}"
          data-meal="breakfast"
          bindtap="onSelectSearchResult"
        >
          <view class="flex-1">
            <view class="text-base font-medium text-primary mb-1">{{item.name}}</view>
            <view class="text-sm text-secondary">{{item.calories}}kcal/100g</view>
          </view>
          <view class="btn btn-sm btn-primary rounded-full w-8 h-8 p-0">+</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日总计 -->
  <view class="card mb-6">
    <view class="card-header">
      <view class="card-title">
        <icon name="📊" size="lg" color="primary"></icon>
        今日营养摄入
      </view>
    </view>

    <view class="card-body">
      <!-- 热量进度环 -->
      <view class="flex justify-center mb-6">
        <circular-progress
          value="{{todayTotal.calories}}"
          max="{{targetCalories}}"
          display-value="{{todayTotal.calories}}"
          label="kcal"
          size="lg"
          progress-color="#10B981"
        ></circular-progress>
      </view>

      <!-- 营养素统计 -->
      <view class="grid grid-cols-3 gap-4">
        <view class="stat-card">
          <view class="stat-icon" style="background: rgba(59, 130, 246, 0.1);">
            <icon name="🥩" size="md" color="info"></icon>
          </view>
          <view class="stat-content">
            <view class="stat-value">{{todayTotal.protein}}</view>
            <view class="stat-label">蛋白质(g)</view>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon" style="background: rgba(245, 158, 11, 0.1);">
            <icon name="🍞" size="md" color="warning"></icon>
          </view>
          <view class="stat-content">
            <view class="stat-value">{{todayTotal.carbs}}</view>
            <view class="stat-label">碳水(g)</view>
          </view>
        </view>

        <view class="stat-card">
          <view class="stat-icon" style="background: rgba(16, 185, 129, 0.1);">
            <icon name="🥑" size="md" color="success"></icon>
          </view>
          <view class="stat-content">
            <view class="stat-value">{{todayTotal.fat}}</view>
            <view class="stat-label">脂肪(g)</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 餐次列表 -->
  <view class="meals-container">
    <view wx:for="{{['breakfast', 'lunch', 'dinner', 'snack']}}" wx:key="*this" class="meal-section">
      <view class="meal-header">
        <view class="meal-info">
          <text class="meal-icon">{{meals[item].icon}}</text>
          <text class="meal-name">{{meals[item].name}}</text>
          <text class="meal-calories">{{meals[item].totalCalories}}kcal</text>
        </view>
        
        <!-- 快捷添加按钮 -->
        <view class="quick-add-buttons">
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onTakePhoto"
          >
            📷
          </view>
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onVoiceRecord"
          >
            🎤
          </view>
          <view 
            class="quick-add-btn"
            data-meal="{{item}}"
            bindtap="onScanBarcode"
          >
            📱
          </view>
        </view>
      </view>

      <!-- 食物列表 -->
      <view class="food-list">
        <view wx:if="{{meals[item].foods.length === 0}}" class="empty-meal">
          <view class="empty-text">暂无记录</view>
          <view class="empty-hint">点击上方按钮添加食物</view>
        </view>
        
        <view wx:else>
          <view 
            wx:for="{{meals[item].foods}}" 
            wx:for-item="food"
            wx:for-index="foodIndex"
            wx:key="foodIndex"
            class="food-item"
          >
            <view class="food-main">
              <view class="food-details">
                <view class="food-name">{{food.name}}</view>
                <view class="food-amount">{{food.amount}}{{food.unit}}</view>
              </view>
              <view class="food-nutrition">
                <view class="food-calories">{{food.calories}}kcal</view>
                <view class="food-macros">
                  蛋白质{{food.protein}}g | 碳水{{food.carbs}}g | 脂肪{{food.fat}}g
                </view>
              </view>
            </view>
            
            <view class="food-actions">
              <view class="record-type">{{food.recordType === 'camera' ? '📷' : '✏️'}}</view>
              <view 
                class="delete-btn"
                data-meal="{{item}}"
                data-index="{{foodIndex}}"
                bindtap="onDeleteFood"
              >
                🗑️
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-btn" bindtap="onViewNutrition">
      <view class="action-icon">📈</view>
      <view class="action-text">营养分析</view>
    </view>
  </view>
</view>
