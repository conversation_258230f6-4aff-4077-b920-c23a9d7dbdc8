/* 智食派小程序全局样式 - 基于健康主题设计系统 */

/* ===== 设计系统变量 ===== */
page {
  /* 主色调 - 健康绿 */
  --primary: #10B981;
  --primary-light: #34D399;
  --primary-dark: #059669;
  --primary-50: #ECFDF5;
  --primary-100: #D1FAE5;
  --primary-500: #10B981;
  --primary-600: #059669;
  --primary-900: #064E3B;

  /* 辅助色 - 活力橙 */
  --accent: #F59E0B;
  --accent-light: #FCD34D;
  --accent-dark: #D97706;
  --accent-50: #FFFBEB;
  --accent-100: #FEF3C7;
  --accent-500: #F59E0B;
  --accent-600: #D97706;

  /* 中性色系 */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* 功能色 */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;

  /* 背景色 */
  --bg-primary: #F9FAFB;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #F3F4F6;

  /* 文字色 */
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --text-inverse: #FFFFFF;

  /* 边框色 */
  --border-light: #F3F4F6;
  --border-default: #E5E7EB;
  --border-strong: #D1D5DB;

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
  --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);

  /* 字体大小 */
  --text-xs: 24rpx;    /* 12px */
  --text-sm: 28rpx;    /* 14px */
  --text-base: 32rpx;  /* 16px */
  --text-lg: 36rpx;    /* 18px */
  --text-xl: 40rpx;    /* 20px */
  --text-2xl: 48rpx;   /* 24px */
  --text-3xl: 60rpx;   /* 30px */

  /* 字重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* 间距系统 - 基于8rpx */
  --space-1: 8rpx;     /* 4px */
  --space-2: 16rpx;    /* 8px */
  --space-3: 24rpx;    /* 12px */
  --space-4: 32rpx;    /* 16px */
  --space-6: 48rpx;    /* 24px */
  --space-8: 64rpx;    /* 32px */
  --space-12: 96rpx;   /* 48px */
  --space-16: 128rpx;  /* 64px */

  /* 圆角 */
  --radius-sm: 8rpx;   /* 4px */
  --radius-md: 16rpx;  /* 8px */
  --radius-lg: 24rpx;  /* 12px */
  --radius-xl: 32rpx;  /* 16px */
  --radius-full: 9999rpx;

  /* 动画时间 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* 缓动函数 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 基础样式重置 ===== */
view, text, image, input, button {
  box-sizing: border-box;
}

page {
  background-color: var(--bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  font-size: var(--text-base);
}

/* ===== 布局容器 ===== */
.container {
  padding: var(--space-4);
}

.page-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
}

.section {
  margin-bottom: var(--space-6);
}

.section:last-child {
  margin-bottom: 0;
}

/* ===== 卡片组件系统 ===== */
.card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.card-compact {
  padding: var(--space-4);
}

.card-spacious {
  padding: var(--space-8);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  margin: 0;
}

.card-title .icon {
  margin-right: var(--space-2);
  color: var(--primary);
  font-size: var(--text-xl);
}

.card-action {
  background: none;
  border: none;
  padding: var(--space-1);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
}

.card-action:active {
  background: var(--gray-100);
}

.card-body {
  color: var(--text-primary);
}

.card-footer {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-light);
}

/* ===== 渐变背景 ===== */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
  color: var(--text-inverse);
}

.gradient-success {
  background: linear-gradient(135deg, var(--success) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
}

/* ===== 按钮组件系统 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  text-align: center;
  border: none;
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
  min-height: 88rpx;
}

.btn:active {
  transform: scale(0.98);
}

.btn-primary {
  background-color: var(--primary);
  color: var(--text-inverse);
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.btn-secondary:active {
  background-color: var(--gray-200);
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--border-default);
  color: var(--text-primary);
}

.btn-outline:active {
  background-color: var(--gray-50);
  border-color: var(--border-strong);
}

.btn-text {
  background-color: transparent;
  color: var(--primary);
  padding: var(--space-2) var(--space-3);
}

.btn-text:active {
  background-color: var(--primary-50);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  min-height: 64rpx;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  min-height: 96rpx;
}

.btn-round {
  border-radius: var(--radius-full);
  width: 88rpx;
  height: 88rpx;
  padding: 0;
}

.btn-icon {
  margin-right: var(--space-2);
}

/* ===== 进度条组件 ===== */
.progress-bar {
  width: 100%;
  height: var(--space-2);
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-track {
  width: 100%;
  height: 100%;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-out);
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.progress-success {
  background: linear-gradient(90deg, var(--success), var(--primary));
}

.progress-warning {
  background: linear-gradient(90deg, var(--warning), var(--accent-dark));
}

.progress-error {
  background: linear-gradient(90deg, var(--error), #DC2626);
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-1);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* ===== 统计卡片组件 ===== */
.stat-card {
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
  font-size: var(--text-xl);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.stat-change {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.stat-change.positive {
  color: var(--success);
}

.stat-change.negative {
  color: var(--error);
}

/* ===== 文本样式系统 ===== */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-inverse {
  color: var(--text-inverse);
}

.text-success {
  color: var(--success);
}

.text-warning {
  color: var(--warning);
}

.text-error {
  color: var(--error);
}

.text-info {
  color: var(--info);
}

/* 文本对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: var(--text-xs);
}

.text-sm {
  font-size: var(--text-sm);
}

.text-base {
  font-size: var(--text-base);
}

.text-lg {
  font-size: var(--text-lg);
}

.text-xl {
  font-size: var(--text-xl);
}

.text-2xl {
  font-size: var(--text-2xl);
}

.text-3xl {
  font-size: var(--text-3xl);
}

/* 字体粗细 */
.font-normal {
  font-weight: var(--font-normal);
}

.font-medium {
  font-weight: var(--font-medium);
}

.font-semibold {
  font-weight: var(--font-semibold);
}

.font-bold {
  font-weight: var(--font-bold);
}

/* ===== 布局系统 ===== */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: var(--space-3);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.gap-1 {
  gap: var(--space-1);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

/* ===== 间距系统 ===== */
/* Margin */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--space-1); margin-right: var(--space-1); }
.mx-2 { margin-left: var(--space-2); margin-right: var(--space-2); }
.mx-3 { margin-left: var(--space-3); margin-right: var(--space-3); }
.mx-4 { margin-left: var(--space-4); margin-right: var(--space-4); }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--space-1); margin-bottom: var(--space-1); }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-3 { margin-top: var(--space-3); margin-bottom: var(--space-3); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }

/* Padding */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }

/* ===== 圆角系统 ===== */
.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: var(--radius-full);
}

/* ===== 阴影系统 ===== */
.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* ===== 动画系统 ===== */
.transition {
  transition: all var(--duration-normal) var(--ease-out);
}

.transition-fast {
  transition: all var(--duration-fast) var(--ease-out);
}

.transition-slow {
  transition: all var(--duration-slow) var(--ease-out);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

/* ===== 交互状态 ===== */
.hover-scale:active {
  transform: scale(0.95);
}

.hover-opacity:active {
  opacity: 0.8;
}

.hover-bg:active {
  background-color: var(--gray-50);
}

/* ===== 食物组件样式 ===== */
.food-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
  border: 1px solid var(--border-light);
  transition: all var(--duration-fast) var(--ease-out);
}

.food-item:active {
  background: var(--gray-50);
  transform: scale(0.98);
}

.food-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-lg);
  margin-right: var(--space-3);
  background: var(--gray-100);
  object-fit: cover;
}

.food-info {
  flex: 1;
  min-width: 0;
}

.food-name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.food-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.food-nutrition {
  display: flex;
  gap: var(--space-2);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.food-calories {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--primary);
  text-align: right;
}

.food-unit {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-1);
}

/* ===== 输入框组件 ===== */
.input-group {
  margin-bottom: var(--space-4);
}

.input-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.input-field {
  width: 100%;
  padding: var(--space-3);
  border: 2rpx solid var(--border-default);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--bg-secondary);
  transition: all var(--duration-fast) var(--ease-out);
}

.input-field:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 6rpx var(--primary-50);
  outline: none;
}

.input-field::placeholder {
  color: var(--text-tertiary);
}

.input-error {
  font-size: var(--text-xs);
  color: var(--error);
  margin-top: var(--space-1);
}

.input-field.error {
  border-color: var(--error);
}

.input-field.error:focus {
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}
