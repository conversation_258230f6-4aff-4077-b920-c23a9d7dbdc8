# 智食派小程序开发指南

## 🚀 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 确保已登录云开发环境：`cloud1-7gfrephg752b4359`

### 2. 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录（包含 `project.config.json` 的目录）
4. AppID 使用：`wx60beed2249cb1bff`（测试用）

### 3. 云开发配置
项目已配置云开发环境，主要配置：
- 环境ID: `cloud1-7gfrephg752b4359`
- 小程序根目录: `miniprogram/`
- 云函数根目录: `cloudfunctions/`

### 4. 项目结构
```
zhishipai/
├── miniprogram/           # 小程序前端代码
│   ├── pages/            # 页面目录
│   ├── utils/            # 工具函数
│   ├── images/           # 图片资源
│   └── app.js/json/wxss  # 小程序配置
├── cloudfunctions/       # 云函数目录
├── project.config.json   # 项目配置
└── cloudbaserc.json     # 云开发配置
```

## 📱 当前功能状态

### ✅ 已完成
- **项目配置**: 完整的小程序项目配置，修复WXSS编译错误
- **云函数部署**: 已部署 user、food、sport、analysis、ai 五个核心云函数
- **数据库配置**: 已创建所有数据集合并配置权限
- **前端界面**: 完整的UI界面，符合设计稿要求
  - 智能仪表盘：个人档案、热量平衡、营养分布、快捷操作
  - 饮食记录页面：餐次管理、食物搜索、营养分析
  - AI拍照识别页面：相机界面、识别结果、智能建议
  - 数据报告页面：体重趋势、热量平衡、习惯分析、减重预测
  - 个人中心页面：档案管理、功能菜单
- **工具函数**: 完整的算法计算、API封装、常量定义
- **样式系统**: 现代化UI设计，响应式布局，符合设计规范
- **基础数据**: 已初始化食物库数据

### 🚧 开发中
- **AI功能**: 食物识别功能使用模拟数据，需要集成真实AI服务
- **图标资源**: 需要替换占位图标为实际图标
- **数据完善**: 需要添加更多食物库数据

### 📋 待优化
- **第三方AI集成**: 集成真实的食物识别API
- **性能优化**: 优化加载速度和用户体验
- **错误处理**: 完善网络异常和边界情况处理
- **测试完善**: 添加更多测试用例

## 🛠️ 开发说明

### 当前可以测试的功能
1. **完整的前端界面**: 所有页面都可以正常显示和交互
2. **云函数调用**: 用户档案、饮食记录、运动记录等功能正常
3. **数据存储**: 可以正常保存和读取用户数据
4. **AI识别模拟**: 拍照识别功能使用模拟数据演示
5. **数据分析**: 体重趋势、热量平衡等分析功能
6. **响应式设计**: 适配不同屏幕尺寸

### 模拟数据和真实功能
- **真实功能**: 用户档案管理、饮食记录、运动记录、数据分析
- **模拟功能**: AI食物识别（使用模拟数据）
- **基础数据**: 已添加常见食物的营养数据

### 云函数状态
- ✅ `user` 云函数：完整实现，处理用户档案和体重记录
- ✅ `food` 云函数：完整实现，处理饮食记录和食物搜索
- ✅ `sport` 云函数：完整实现，处理运动记录
- ✅ `analysis` 云函数：完整实现，提供数据分析功能
- ✅ `ai` 云函数：基础实现，使用模拟数据进行食物识别

## 🔧 开发建议

### 1. 立即可以做的
- 在微信开发者工具中预览界面效果
- 测试页面导航和基础交互
- 查看响应式设计效果
- 体验整体UI风格

### 2. 下一步开发重点
- 完善云函数业务逻辑
- 创建和配置数据库集合
- 添加真实的图标资源
- 集成AI识别服务

### 3. 测试建议
- 在不同设备上测试界面适配
- 测试各种交互场景
- 验证数据流转逻辑
- 检查错误处理机制

## 📞 技术支持

如果在开发过程中遇到问题：
1. 检查微信开发者工具控制台的错误信息
2. 确认云开发环境是否正常
3. 查看项目文档和代码注释
4. 参考微信小程序官方文档

---

**智食派** - 让减重更科学，让健康更简单！ 🎯
