{"envId": "cloud1-7gfrephg752b4359", "version": "2.0", "framework": {"name": "miniprogram", "plugins": {"client": {"use": "@cloudbase/framework-plugin-mp", "inputs": {"appid": "wx1234567890abcdef", "privateKeyPath": "./private.key"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./cloudfunctions", "functions": [{"name": "user", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}, {"name": "food", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}, {"name": "sport", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}, {"name": "analysis", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}, {"name": "ai", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 512}]}}}}}