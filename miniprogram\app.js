// 智食派小程序入口文件
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-7gfrephg752b4359', // 云开发环境ID
        traceUser: true,
      });
    }

    // 获取用户信息
    this.getUserProfile();
  },

  onShow() {
    // 小程序显示时更新数据
    this.updateGlobalData();
  },

  // 获取用户档案
  getUserProfile() {
    const that = this;
    wx.cloud.callFunction({
      name: 'user',
      data: {
        action: 'getUserProfile'
      },
      success: res => {
        if (res.result.success) {
          that.globalData.userProfile = res.result.data;
        }
      },
      fail: err => {
        console.error('获取用户档案失败', err);
      }
    });
  },

  // 更新全局数据
  updateGlobalData() {
    // 更新今日数据
    this.getTodayData();
  },

  // 获取今日数据
  getTodayData() {
    const that = this;
    const today = new Date().toISOString().split('T')[0];
    
    // 获取今日饮食记录
    wx.cloud.callFunction({
      name: 'food',
      data: {
        action: 'getFoodRecords',
        date: today
      },
      success: res => {
        if (res.result.success) {
          that.globalData.todayFoodRecords = res.result.data;
          that.calculateTodayNutrition();
        }
      }
    });

    // 获取今日运动记录
    wx.cloud.callFunction({
      name: 'food',
      data: {
        action: 'getSportRecords',
        date: today
      },
      success: res => {
        if (res.result.success) {
          that.globalData.todaySportRecords = res.result.data;
        }
      }
    });
  },

  // 计算今日营养摄入
  calculateTodayNutrition() {
    const records = this.globalData.todayFoodRecords || [];
    let totalCalories = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFat = 0;

    records.forEach(record => {
      if (record.foods && Array.isArray(record.foods)) {
        record.foods.forEach(food => {
          totalCalories += food.calories || 0;
          totalProtein += food.protein || 0;
          totalCarbs += food.carbs || 0;
          totalFat += food.fat || 0;
        });
      }
    });

    this.globalData.todayNutrition = {
      calories: totalCalories,
      protein: totalProtein,
      carbs: totalCarbs,
      fat: totalFat
    };
  },

  // 全局数据
  globalData: {
    userProfile: null,
    todayFoodRecords: [],
    todaySportRecords: [],
    todayNutrition: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    }
  }
});
