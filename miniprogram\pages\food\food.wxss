/* 智食派饮食记录页面样式 */

/* 搜索容器 */
.search-container {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--bg-primary);
  padding: 32rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.search-box {
  position: relative;
  background: white;
  border-radius: 24rpx;
  box-shadow: var(--shadow);
}

.search-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 32rpx;
  font-size: 28rpx;
  border: none;
  border-radius: 24rpx;
}

.search-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: var(--text-secondary);
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: var(--shadow);
  max-height: 400rpx;
  overflow-y: auto;
  z-index: 101;
}

.search-loading,
.search-empty {
  padding: 32rpx;
  text-align: center;
  color: var(--text-secondary);
  font-size: 28rpx;
}

.search-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:active {
  background: var(--bg-primary);
}

.add-btn {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

/* 今日总计卡片 */
.total-card {
  margin: 32rpx;
  margin-bottom: 16rpx;
}

.total-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.total-item {
  text-align: center;
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.total-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.total-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 餐次容器 */
.meals-container {
  padding: 0 32rpx 32rpx;
}

.meal-section {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  overflow: hidden;
}

/* 餐次头部 */
.meal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2rpx solid var(--border-color);
}

.meal-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.meal-icon {
  font-size: 32rpx;
}

.meal-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.meal-calories {
  font-size: 28rpx;
  color: var(--text-secondary);
  background: rgba(37, 99, 235, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 快捷添加按钮 */
.quick-add-buttons {
  display: flex;
  gap: 16rpx;
}

.quick-add-btn {
  width: 64rpx;
  height: 64rpx;
  background: white;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.quick-add-btn:active {
  transform: scale(0.9);
  background: var(--bg-primary);
}

/* 食物列表 */
.food-list {
  padding: 0 32rpx 32rpx;
}

.empty-meal {
  text-align: center;
  padding: 64rpx 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 食物条目 */
.food-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.food-item:last-child {
  border-bottom: none;
}

.food-main {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.food-details {
  flex: 1;
}

.food-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.food-amount {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.food-nutrition {
  text-align: right;
}

.food-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.food-macros {
  font-size: 20rpx;
  color: var(--text-secondary);
  line-height: 1.2;
}

/* 食物操作 */
.food-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: 24rpx;
}

.record-type {
  font-size: 24rpx;
  opacity: 0.6;
}

.delete-btn {
  font-size: 24rpx;
  color: var(--danger-color);
  padding: 8rpx;
}

.delete-btn:active {
  opacity: 0.6;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid var(--border-color);
  box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 24rpx;
  font-weight: 600;
}

.action-btn:active {
  opacity: 0.8;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 28rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .total-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .total-value {
    font-size: 28rpx;
  }
  
  .total-label {
    font-size: 22rpx;
  }
  
  .meal-header {
    padding: 24rpx;
  }
  
  .quick-add-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 24rpx;
  }
  
  .food-macros {
    font-size: 18rpx;
  }
}
