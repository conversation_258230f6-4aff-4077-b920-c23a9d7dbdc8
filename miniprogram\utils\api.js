// 智食派API封装工具

/**
 * 云函数调用封装
 * @param {string} name - 云函数名称
 * @param {object} data - 调用参数
 * @returns {Promise} 调用结果
 */
function callCloudFunction(name, data) {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name: name,
      data: data,
      success: res => {
        if (res.result && res.result.success) {
          resolve(res.result.data);
        } else {
          reject(res.result ? res.result.error : '调用失败');
        }
      },
      fail: err => {
        console.error(`云函数 ${name} 调用失败:`, err);
        reject(err);
      }
    });
  });
}

/**
 * 显示加载提示
 * @param {string} title - 提示文字
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  });
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示成功提示
 * @param {string} title - 提示文字
 */
function showSuccess(title) {
  wx.showToast({
    title: title,
    icon: 'success',
    duration: 2000
  });
}

/**
 * 显示错误提示
 * @param {string} title - 提示文字
 */
function showError(title) {
  wx.showToast({
    title: title,
    icon: 'error',
    duration: 2000
  });
}

/**
 * 显示普通提示
 * @param {string} title - 提示文字
 */
function showToast(title) {
  wx.showToast({
    title: title,
    icon: 'none',
    duration: 2000
  });
}

/**
 * 用户相关API
 */
const userAPI = {
  // 获取用户档案
  getUserProfile() {
    return callCloudFunction('user', {
      action: 'getUserProfile'
    });
  },

  // 更新用户档案
  updateUserProfile(profile) {
    return callCloudFunction('user', {
      action: 'updateUserProfile',
      profile: profile
    });
  },

  // 记录体重
  addWeightRecord(weight) {
    return callCloudFunction('user', {
      action: 'addWeightRecord',
      weight: weight,
      date: new Date().toISOString().split('T')[0]
    });
  },

  // 获取体重记录
  getWeightRecords(days = 30) {
    return callCloudFunction('user', {
      action: 'getWeightRecords',
      days: days
    });
  }
};

/**
 * 饮食相关API
 */
const foodAPI = {
  // 添加饮食记录
  addFoodRecord(meal, foods, recordType = 'manual') {
    return callCloudFunction('food', {
      action: 'addFoodRecord',
      meal: meal,
      foods: foods,
      recordType: recordType,
      date: new Date().toISOString().split('T')[0]
    });
  },

  // 获取饮食记录
  getFoodRecords(date) {
    return callCloudFunction('food', {
      action: 'getFoodRecords',
      date: date || new Date().toISOString().split('T')[0]
    });
  },

  // 搜索食物
  searchFood(keyword) {
    return callCloudFunction('food', {
      action: 'searchFood',
      keyword: keyword
    });
  },

  // 获取每日营养分析
  getDailyNutrition(date) {
    return callCloudFunction('food', {
      action: 'getDailyNutrition',
      date: date || new Date().toISOString().split('T')[0]
    });
  }
};

/**
 * 运动相关API
 */
const sportAPI = {
  // 添加运动记录
  addSportRecord(sportType, duration, calories) {
    return callCloudFunction('sport', {
      action: 'addSportRecord',
      sportType: sportType,
      duration: duration,
      calories: calories,
      date: new Date().toISOString().split('T')[0]
    });
  },

  // 获取运动记录
  getSportRecords(date) {
    return callCloudFunction('sport', {
      action: 'getSportRecords',
      date: date || new Date().toISOString().split('T')[0]
    });
  },

  // 获取运动类型列表
  getSportTypes() {
    return callCloudFunction('sport', {
      action: 'getSportTypes'
    });
  }
};

/**
 * 数据分析相关API
 */
const analysisAPI = {
  // 获取体重趋势
  getWeightTrend(days = 30) {
    return callCloudFunction('analysis', {
      action: 'getWeightTrend',
      days: days
    });
  },

  // 获取热量平衡分析
  getCalorieBalance(days = 7) {
    return callCloudFunction('analysis', {
      action: 'getCalorieBalance',
      days: days
    });
  },

  // 获取习惯分析
  getHabitAnalysis(days = 30) {
    return callCloudFunction('analysis', {
      action: 'getHabitAnalysis',
      days: days
    });
  },

  // 预测减重进度
  predictWeightLoss() {
    return callCloudFunction('analysis', {
      action: 'predictWeightLoss'
    });
  }
};

/**
 * AI识别相关API
 */
const aiAPI = {
  // 识别食物图片
  recognizeFood(imageUrl) {
    return callCloudFunction('ai', {
      action: 'recognizeFood',
      imageUrl: imageUrl
    });
  },

  // 获取智能建议
  getRecommendations(foodData) {
    return callCloudFunction('ai', {
      action: 'getRecommendations',
      foodData: foodData
    });
  }
};

/**
 * 上传图片到云存储
 * @param {string} filePath - 本地文件路径
 * @param {string} cloudPath - 云存储路径
 * @returns {Promise} 上传结果
 */
function uploadImage(filePath, cloudPath) {
  return new Promise((resolve, reject) => {
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        resolve(res.fileID);
      },
      fail: err => {
        console.error('图片上传失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 选择并上传图片
 * @param {number} count - 选择图片数量
 * @returns {Promise} 上传结果
 */
function chooseAndUploadImage(count = 1) {
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePaths = res.tempFilePaths;
        const uploadPromises = tempFilePaths.map((filePath, index) => {
          const cloudPath = `images/${Date.now()}_${index}.jpg`;
          return uploadImage(filePath, cloudPath);
        });

        Promise.all(uploadPromises)
          .then(fileIDs => {
            resolve(fileIDs);
          })
          .catch(reject);
      },
      fail: reject
    });
  });
}

module.exports = {
  callCloudFunction,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showToast,
  userAPI,
  foodAPI,
  sportAPI,
  analysisAPI,
  aiAPI,
  uploadImage,
  chooseAndUploadImage
};
