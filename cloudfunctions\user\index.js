// 用户管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action } = event;

  try {
    switch (action) {
      case 'getUserProfile':
        return await getUserProfile(wxContext.OPENID);
      case 'updateUserProfile':
        return await updateUserProfile(wxContext.OPENID, event.profile);
      case 'addWeightRecord':
        return await addWeightRecord(wxContext.OPENID, event.weight, event.date);
      case 'getWeightRecords':
        return await getWeightRecords(wxContext.OPENID, event.days);
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { success: false, error: error.message };
  }
};

// 获取用户档案
async function getUserProfile(openid) {
  const result = await db.collection('users').where({
    _openid: openid
  }).get();

  if (result.data.length > 0) {
    return { success: true, data: result.data[0] };
  } else {
    return { success: false, error: '用户档案不存在' };
  }
}

// 更新用户档案
async function updateUserProfile(openid, profile) {
  const result = await db.collection('users').where({
    _openid: openid
  }).get();

  if (result.data.length > 0) {
    // 更新现有档案
    await db.collection('users').doc(result.data[0]._id).update({
      data: {
        ...profile,
        updatedAt: new Date()
      }
    });
  } else {
    // 创建新档案
    await db.collection('users').add({
      data: {
        _openid: openid,
        ...profile,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  return { success: true, data: profile };
}

// 添加体重记录
async function addWeightRecord(openid, weight, date) {
  await db.collection('weight_records').add({
    data: {
      _openid: openid,
      weight: weight,
      date: date,
      createdAt: new Date()
    }
  });

  return { success: true, data: { weight, date } };
}

// 获取体重记录
async function getWeightRecords(openid, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const result = await db.collection('weight_records')
    .where({
      _openid: openid,
      date: db.command.gte(startDate.toISOString().split('T')[0])
    })
    .orderBy('date', 'asc')
    .get();

  return { success: true, data: result.data };
}
