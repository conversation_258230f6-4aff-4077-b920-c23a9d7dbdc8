<!-- 智食派数据报告页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 体重趋势 -->
    <view class="card">
      <view class="card-title">
        <text class="icon">📈</text>
        体重趋势
      </view>

      <view class="weight-summary">
        <view class="weight-item">
          <view class="weight-value">{{weightTrend.currentWeight}}kg</view>
          <view class="weight-label">当前体重</view>
        </view>
        <view class="weight-item">
          <view class="weight-value" style="color: {{weightTrend.weightChange < 0 ? '#10b981' : '#ef4444'}}">
            {{weightTrend.weightChange > 0 ? '+' : ''}}{{weightTrend.weightChange}}kg
          </view>
          <view class="weight-label">变化</view>
        </view>
        <view class="weight-item">
          <view class="weight-value">{{weightTrend.weeklyRate}}kg/周</view>
          <view class="weight-label">减重速度</view>
        </view>
      </view>

      <view class="health-status" style="color: {{weightTrend.healthStatus.color}}">
        {{weightTrend.healthStatus.message}}
      </view>
    </view>

    <!-- 热量平衡 -->
    <view class="card">
      <view class="card-title">
        <text class="icon">⚖️</text>
        热量平衡
      </view>

      <view class="calorie-summary">
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgIntake}}</view>
          <view class="calorie-label">平均摄入</view>
        </view>
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgBurn}}</view>
          <view class="calorie-label">平均消耗</view>
        </view>
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgDeficit}}</view>
          <view class="calorie-label">热量缺口</view>
        </view>
      </view>
    </view>

    <!-- 习惯分析 -->
    <view class="card">
      <view class="card-title">
        <text class="icon">📋</text>
        习惯分析
      </view>

      <view class="habit-stats">
        <view class="habit-item">
          <view class="habit-percentage">{{habitAnalysis.recordFrequency}}%</view>
          <view class="habit-label">记录频率</view>
        </view>
      </view>
    </view>

    <!-- 减重预测 -->
    <view class="card">
      <view class="card-title">
        <text class="icon">🎯</text>
        减重预测
      </view>

      <view class="prediction-content">
        <view class="prediction-item">
          <view class="prediction-value">{{prediction.currentProgress}}%</view>
          <view class="prediction-label">目标完成度</view>
        </view>
        <view class="prediction-item">
          <view class="prediction-value">{{prediction.estimatedWeeks}}周</view>
          <view class="prediction-label">预计剩余时间</view>
        </view>
      </view>
    </view>
  </view>
</view>