// 智食派饮食记录页面
const { foodAPI, showLoading, hideLoading, showSuccess, showError } = require('../../utils/api');
const { getTodayDate } = require('../../utils/calculator');
const { MEAL_TYPES, MEAL_NAMES, MEAL_ICONS, RECORD_TYPES, RECORD_TYPE_NAMES } = require('../../utils/constants');

Page({
  data: {
    // 当前日期
    currentDate: '',
    
    // 餐次数据
    meals: {
      breakfast: { name: MEAL_NAMES.breakfast, icon: MEAL_ICONS.breakfast, foods: [], totalCalories: 0 },
      lunch: { name: MEAL_NAMES.lunch, icon: MEAL_ICONS.lunch, foods: [], totalCalories: 0 },
      dinner: { name: MEAL_NAMES.dinner, icon: MEAL_ICONS.dinner, foods: [], totalCalories: 0 },
      snack: { name: MEAL_NAMES.snack, icon: MEAL_ICONS.snack, foods: [], totalCalories: 0 }
    },
    
    // 今日总计
    todayTotal: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    },
    
    // 搜索相关
    searchKeyword: '',
    searchResults: [],
    showSearchResults: false,
    
    // 加载状态
    loading: true,
    searching: false
  },

  onLoad() {
    this.setData({
      currentDate: getTodayDate()
    });
    this.loadFoodRecords();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadFoodRecords();
  },

  onPullDownRefresh() {
    this.loadFoodRecords().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载饮食记录
  async loadFoodRecords() {
    try {
      showLoading('加载中...');
      const records = await foodAPI.getFoodRecords(this.data.currentDate);
      this.processFoodRecords(records);
    } catch (error) {
      console.error('加载饮食记录失败:', error);
      // 使用模拟数据进行演示
      const mockRecords = [
        {
          meal: 'breakfast',
          foods: [
            { name: '燕麦粥', amount: 200, unit: 'g', calories: 180, protein: 6.2, carbs: 32.1, fat: 2.8 }
          ],
          recordType: 'manual'
        }
      ];
      this.processFoodRecords(mockRecords);
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 处理饮食记录数据
  processFoodRecords(records) {
    const meals = {
      breakfast: { name: MEAL_NAMES.breakfast, icon: MEAL_ICONS.breakfast, foods: [], totalCalories: 0 },
      lunch: { name: MEAL_NAMES.lunch, icon: MEAL_ICONS.lunch, foods: [], totalCalories: 0 },
      dinner: { name: MEAL_NAMES.dinner, icon: MEAL_ICONS.dinner, foods: [], totalCalories: 0 },
      snack: { name: MEAL_NAMES.snack, icon: MEAL_ICONS.snack, foods: [], totalCalories: 0 }
    };

    let totalCalories = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFat = 0;

    records.forEach(record => {
      const meal = record.meal;
      if (meals[meal] && record.foods) {
        record.foods.forEach(food => {
          meals[meal].foods.push({
            ...food,
            recordType: record.recordType,
            recordId: record._id
          });
          meals[meal].totalCalories += food.calories || 0;
          
          totalCalories += food.calories || 0;
          totalProtein += food.protein || 0;
          totalCarbs += food.carbs || 0;
          totalFat += food.fat || 0;
        });
      }
    });

    this.setData({
      meals,
      todayTotal: {
        calories: totalCalories,
        protein: totalProtein,
        carbs: totalCarbs,
        fat: totalFat
      }
    });
  },

  // 搜索食物
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    if (keyword.trim()) {
      this.searchFood(keyword);
    } else {
      this.setData({ 
        searchResults: [],
        showSearchResults: false 
      });
    }
  },

  // 执行搜索
  async searchFood(keyword) {
    try {
      this.setData({ searching: true });
      const results = await foodAPI.searchFood(keyword);
      this.setData({ 
        searchResults: results,
        showSearchResults: true 
      });
    } catch (error) {
      console.error('搜索食物失败:', error);
      showError('搜索失败');
    } finally {
      this.setData({ searching: false });
    }
  },

  // 选择搜索结果
  onSelectSearchResult(e) {
    const { food, meal } = e.currentTarget.dataset;
    this.showAddFoodDialog(food, meal);
  },

  // 显示添加食物对话框
  showAddFoodDialog(food, meal) {
    wx.showModal({
      title: `添加到${MEAL_NAMES[meal]}`,
      content: `${food.name}\n热量: ${food.calories}kcal/100g`,
      editable: true,
      placeholderText: '请输入重量(g)',
      success: async (res) => {
        if (res.confirm && res.content) {
          const amount = parseFloat(res.content);
          if (amount > 0) {
            await this.addFoodRecord(meal, food, amount);
          } else {
            showError('请输入有效的重量');
          }
        }
      }
    });
  },

  // 添加食物记录
  async addFoodRecord(meal, food, amount) {
    try {
      showLoading('添加中...');
      
      // 计算实际营养值
      const ratio = amount / 100;
      const actualFood = {
        name: food.name,
        amount: amount,
        unit: 'g',
        calories: Math.round(food.calories * ratio),
        protein: Math.round((food.protein || 0) * ratio * 10) / 10,
        carbs: Math.round((food.carbs || 0) * ratio * 10) / 10,
        fat: Math.round((food.fat || 0) * ratio * 10) / 10,
        fiber: Math.round((food.fiber || 0) * ratio * 10) / 10
      };

      await foodAPI.addFoodRecord(meal, [actualFood], RECORD_TYPES.MANUAL);
      
      showSuccess('添加成功');
      this.setData({ 
        searchKeyword: '',
        searchResults: [],
        showSearchResults: false 
      });
      
      // 刷新数据
      this.loadFoodRecords();
    } catch (error) {
      console.error('添加食物记录失败:', error);
      showError('添加失败');
    } finally {
      hideLoading();
    }
  },

  // 快捷添加 - 拍照识别
  onTakePhoto(e) {
    const meal = e.currentTarget.dataset.meal;
    wx.navigateTo({
      url: `/pages/camera/camera?meal=${meal}`
    });
  },

  // 快捷添加 - 语音记录
  onVoiceRecord(e) {
    const meal = e.currentTarget.dataset.meal;
    wx.showToast({
      title: '语音记录功能开发中',
      icon: 'none'
    });
  },

  // 快捷添加 - 扫码录入
  onScanBarcode(e) {
    const meal = e.currentTarget.dataset.meal;
    wx.scanCode({
      success: (res) => {
        wx.showToast({
          title: '扫码功能开发中',
          icon: 'none'
        });
      },
      fail: (err) => {
        showError('扫码失败');
      }
    });
  },

  // 删除食物
  onDeleteFood(e) {
    const { meal, index } = e.currentTarget.dataset;
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个食物记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteFoodItem(meal, index);
        }
      }
    });
  },

  // 删除食物项
  async deleteFoodItem(meal, index) {
    try {
      showLoading('删除中...');
      
      // 这里需要调用删除API，暂时先从本地删除
      const meals = { ...this.data.meals };
      meals[meal].foods.splice(index, 1);
      
      // 重新计算总热量
      meals[meal].totalCalories = meals[meal].foods.reduce((sum, food) => sum + (food.calories || 0), 0);
      
      this.setData({ meals });
      this.recalculateTotal();
      
      showSuccess('删除成功');
    } catch (error) {
      console.error('删除食物失败:', error);
      showError('删除失败');
    } finally {
      hideLoading();
    }
  },

  // 重新计算总计
  recalculateTotal() {
    const meals = this.data.meals;
    let totalCalories = 0;
    let totalProtein = 0;
    let totalCarbs = 0;
    let totalFat = 0;

    Object.values(meals).forEach(meal => {
      meal.foods.forEach(food => {
        totalCalories += food.calories || 0;
        totalProtein += food.protein || 0;
        totalCarbs += food.carbs || 0;
        totalFat += food.fat || 0;
      });
    });

    this.setData({
      todayTotal: {
        calories: totalCalories,
        protein: totalProtein,
        carbs: totalCarbs,
        fat: totalFat
      }
    });
  },

  // 查看营养详情
  onViewNutrition() {
    wx.navigateTo({
      url: '/pages/nutrition/nutrition'
    });
  }
});
