/* 环形进度条样式 */

.circular-progress {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 尺寸变体 */
.circular-progress.sm {
  width: 120rpx;
  height: 120rpx;
}

.circular-progress.md {
  width: 160rpx;
  height: 160rpx;
}

.circular-progress.lg {
  width: 200rpx;
  height: 200rpx;
}

.circular-progress.xl {
  width: 240rpx;
  height: 240rpx;
}

/* 进度环容器 */
.progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progress-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

/* 进度环样式 */
.progress-ring-background {
  opacity: 0.2;
}

.progress-ring-fill {
  transition: stroke-dashoffset var(--duration-normal) var(--ease-out);
}

/* 中心内容 */
.progress-center {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 数值样式 */
.progress-value {
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.progress-value.sm {
  font-size: var(--text-base);
}

.progress-value.md {
  font-size: var(--text-lg);
}

.progress-value.lg {
  font-size: var(--text-xl);
}

.progress-value.xl {
  font-size: var(--text-2xl);
}

/* 标签样式 */
.progress-label {
  color: var(--text-secondary);
  margin-top: var(--space-1);
  line-height: 1;
}

.progress-label.sm {
  font-size: var(--text-xs);
}

.progress-label.md {
  font-size: var(--text-sm);
}

.progress-label.lg {
  font-size: var(--text-base);
}

.progress-label.xl {
  font-size: var(--text-lg);
}
