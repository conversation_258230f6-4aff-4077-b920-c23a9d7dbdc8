// 智食派运动管理页面
const { sportAPI, userAPI, showLoading, hideLoading, showSuccess, showError } = require('../../utils/api');
const { getTodayDate } = require('../../utils/calculator');
const { SPORT_TYPES, SPORT_TYPE_NAMES, SPORT_MET_VALUES } = require('../../utils/constants');

Page({
  data: {
    // 当前日期
    currentDate: '',
    
    // 今日运动记录
    todayRecords: [],
    todayTotalCalories: 0,
    
    // 运动类型
    sportTypes: [],
    
    // 用户信息
    userWeight: 60,
    
    // 添加运动表单
    showAddForm: false,
    selectedSport: null,
    duration: '',
    
    // 运动推荐
    recommendations: [],
    
    // 加载状态
    loading: true,
    adding: false
  },

  onLoad() {
    this.setData({
      currentDate: getTodayDate()
    });
    this.initData();
  },

  onShow() {
    this.loadTodayRecords();
  },

  onPullDownRefresh() {
    this.loadTodayRecords().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 初始化数据
  async initData() {
    try {
      showLoading('加载中...');
      
      // 并行加载数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadSportTypes(),
        this.loadTodayRecords(),
        this.loadRecommendations()
      ]);
      
    } catch (error) {
      console.error('初始化数据失败:', error);
      showError('数据加载失败');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const profile = await userAPI.getUserProfile();
      this.setData({
        userWeight: profile.weight || 60
      });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 加载运动类型
  async loadSportTypes() {
    try {
      // 使用本地运动类型数据
      const sportTypes = Object.keys(SPORT_TYPES).map(key => ({
        type: SPORT_TYPES[key],
        name: SPORT_TYPE_NAMES[SPORT_TYPES[key]],
        metValue: SPORT_MET_VALUES[SPORT_TYPES[key]],
        icon: this.getSportIcon(SPORT_TYPES[key])
      }));
      
      this.setData({ sportTypes });
    } catch (error) {
      console.error('加载运动类型失败:', error);
    }
  },

  // 获取运动图标
  getSportIcon(sportType) {
    const icons = {
      running: '🏃',
      walking: '🚶',
      cycling: '🚴',
      swimming: '🏊',
      fitness: '💪',
      yoga: '🧘',
      basketball: '🏀',
      football: '⚽',
      badminton: '🏸',
      tennis: '🎾'
    };
    return icons[sportType] || '🏃';
  },

  // 加载今日运动记录
  async loadTodayRecords() {
    try {
      const records = await sportAPI.getSportRecords(this.data.currentDate);
      
      let totalCalories = 0;
      records.forEach(record => {
        totalCalories += record.calories || 0;
      });
      
      this.setData({
        todayRecords: records,
        todayTotalCalories: totalCalories
      });
    } catch (error) {
      console.error('加载今日运动记录失败:', error);
    }
  },

  // 加载运动推荐
  async loadRecommendations() {
    try {
      const weight = this.data.userWeight;
      const recommendations = this.data.sportTypes.map(sport => {
        // 计算30分钟的热量消耗
        const calories30min = Math.round(sport.metValue * weight * 0.5);
        return {
          ...sport,
          recommendedDuration: 30,
          estimatedCalories: calories30min,
          benefits: this.getSportBenefits(sport.type)
        };
      }).sort((a, b) => b.estimatedCalories - a.estimatedCalories);
      
      this.setData({ recommendations });
    } catch (error) {
      console.error('加载运动推荐失败:', error);
    }
  },

  // 获取运动益处
  getSportBenefits(sportType) {
    const benefits = {
      running: '高效燃脂，提升心肺功能',
      walking: '温和有氧，适合日常锻炼',
      cycling: '保护关节，锻炼下肢',
      swimming: '全身运动，低冲击性',
      fitness: '增肌塑形，提升代谢',
      yoga: '柔韧性训练，放松身心',
      basketball: '团队运动，提升协调性',
      football: '全身运动，增强体能',
      badminton: '反应训练，燃烧热量',
      tennis: '手眼协调，上肢锻炼'
    };
    return benefits[sportType] || '有益健康';
  },

  // 选择运动类型
  onSelectSport(e) {
    const sport = e.currentTarget.dataset.sport;
    this.setData({
      selectedSport: sport,
      showAddForm: true,
      duration: ''
    });
  },

  // 输入运动时长
  onDurationInput(e) {
    this.setData({
      duration: e.detail.value
    });
  },

  // 计算热量消耗
  calculateCalories(sportType, duration, weight) {
    const metValue = SPORT_MET_VALUES[sportType] || 3.5;
    return Math.round(metValue * weight * (duration / 60));
  },

  // 添加运动记录
  async onAddSportRecord() {
    const { selectedSport, duration, userWeight } = this.data;
    
    if (!selectedSport || !duration) {
      showError('请选择运动类型和输入时长');
      return;
    }
    
    const durationNum = parseFloat(duration);
    if (durationNum <= 0 || durationNum > 600) {
      showError('运动时长应在1-600分钟之间');
      return;
    }
    
    try {
      this.setData({ adding: true });
      showLoading('添加中...');
      
      const calories = this.calculateCalories(selectedSport.type, durationNum, userWeight);
      
      await sportAPI.addSportRecord(selectedSport.type, durationNum, calories);
      
      showSuccess('添加成功');
      
      // 关闭表单并刷新数据
      this.setData({
        showAddForm: false,
        selectedSport: null,
        duration: ''
      });
      
      this.loadTodayRecords();
      
    } catch (error) {
      console.error('添加运动记录失败:', error);
      showError('添加失败');
    } finally {
      this.setData({ adding: false });
      hideLoading();
    }
  },

  // 取消添加
  onCancelAdd() {
    this.setData({
      showAddForm: false,
      selectedSport: null,
      duration: ''
    });
  },

  // 删除运动记录
  onDeleteRecord(e) {
    const { index } = e.currentTarget.dataset;
    const record = this.data.todayRecords[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除这条${SPORT_TYPE_NAMES[record.sportType]}记录吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord(index);
        }
      }
    });
  },

  // 删除记录
  async deleteRecord(index) {
    try {
      showLoading('删除中...');
      
      // 这里需要调用删除API，暂时先从本地删除
      const todayRecords = [...this.data.todayRecords];
      const deletedRecord = todayRecords.splice(index, 1)[0];
      
      const todayTotalCalories = this.data.todayTotalCalories - (deletedRecord.calories || 0);
      
      this.setData({
        todayRecords,
        todayTotalCalories
      });
      
      showSuccess('删除成功');
    } catch (error) {
      console.error('删除运动记录失败:', error);
      showError('删除失败');
    } finally {
      hideLoading();
    }
  },

  // 快速添加推荐运动
  onQuickAdd(e) {
    const recommendation = e.currentTarget.dataset.recommendation;
    this.setData({
      selectedSport: recommendation,
      duration: recommendation.recommendedDuration.toString(),
      showAddForm: true
    });
  },

  // 查看运动历史
  onViewHistory() {
    wx.navigateTo({
      url: '/pages/sport-history/sport-history'
    });
  }
});
