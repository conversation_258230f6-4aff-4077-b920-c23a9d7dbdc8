{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "condition": false, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "swc": false, "disableSWC": true, "lazyCodeLoading": "requiredComponents", "requiredBackgroundModes": [], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "optimization": {"subPackages": true}}, "compileType": "miniprogram", "libVersion": "3.8.9", "appid": "wx60beed2249cb1bff", "projectname": "云开发空白项目", "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "simulatorPluginLibVersion": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "srcMiniprogramRoot": "miniprogram/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}