// 环形进度条组件
Component({
  properties: {
    // 进度值 (0-100)
    value: {
      type: Number,
      value: 0
    },
    // 最大值
    max: {
      type: Number,
      value: 100
    },
    // 显示的数值
    displayValue: {
      type: String,
      value: ''
    },
    // 标签文本
    label: {
      type: String,
      value: ''
    },
    // 尺寸：sm, md, lg, xl
    size: {
      type: String,
      value: 'md'
    },
    // 进度条颜色
    progressColor: {
      type: String,
      value: '#10B981'
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      value: '#E5E7EB'
    },
    // 线条宽度
    strokeWidth: {
      type: Number,
      value: 8
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    radius: 46,
    circumference: 0,
    dashOffset: 0,
    valueSize: 'md',
    labelSize: 'md'
  },

  observers: {
    'value, max': function(value, max) {
      this.updateProgress(value, max);
    },
    'size': function(size) {
      this.updateSizes(size);
    },
    'strokeWidth': function(strokeWidth) {
      this.updateRadius(strokeWidth);
    }
  },

  lifetimes: {
    attached() {
      this.updateRadius(this.data.strokeWidth);
      this.updateSizes(this.data.size);
      this.updateProgress(this.data.value, this.data.max);
    }
  },

  methods: {
    updateRadius(strokeWidth) {
      const radius = 54 - strokeWidth;
      const circumference = 2 * Math.PI * radius;
      
      this.setData({
        radius,
        circumference
      });
    },

    updateSizes(size) {
      const sizeMap = {
        sm: { value: 'sm', label: 'sm' },
        md: { value: 'md', label: 'md' },
        lg: { value: 'lg', label: 'lg' },
        xl: { value: 'xl', label: 'xl' }
      };

      const sizes = sizeMap[size] || sizeMap.md;
      
      this.setData({
        valueSize: sizes.value,
        labelSize: sizes.label
      });
    },

    updateProgress(value, max) {
      const percentage = Math.min(Math.max(value / max, 0), 1);
      const dashOffset = this.data.circumference * (1 - percentage);
      
      this.setData({
        dashOffset
      });
    }
  }
})
