<!-- 环形进度条组件 -->
<view class="circular-progress {{size}}" style="{{customStyle}}">
  <view class="progress-ring">
    <svg class="progress-svg" viewBox="0 0 120 120">
      <!-- 背景圆环 -->
      <circle
        class="progress-ring-background"
        cx="60"
        cy="60"
        r="{{radius}}"
        fill="transparent"
        stroke="{{backgroundColor}}"
        stroke-width="{{strokeWidth}}"
      />
      <!-- 进度圆环 -->
      <circle
        class="progress-ring-fill"
        cx="60"
        cy="60"
        r="{{radius}}"
        fill="transparent"
        stroke="{{progressColor}}"
        stroke-width="{{strokeWidth}}"
        stroke-dasharray="{{circumference}}"
        stroke-dashoffset="{{dashOffset}}"
        stroke-linecap="round"
        transform="rotate(-90 60 60)"
      />
    </svg>
  </view>
  
  <!-- 中心内容 -->
  <view class="progress-center">
    <view class="progress-value {{valueSize}}">{{displayValue}}</view>
    <view wx:if="{{label}}" class="progress-label {{labelSize}}">{{label}}</view>
  </view>
</view>
