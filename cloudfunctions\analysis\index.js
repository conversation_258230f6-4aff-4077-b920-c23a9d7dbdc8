// 数据分析云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action } = event;

  try {
    switch (action) {
      case 'getWeightTrend':
        return await getWeightTrend(wxContext.OPENID, event.days);
      case 'getCalorieBalance':
        return await getCalorieBalance(wxContext.OPENID, event.days);
      case 'getHabitAnalysis':
        return await getHabitAnalysis(wxContext.OPENID, event.days);
      case 'predictWeightLoss':
        return await predictWeightLoss(wxContext.OPENID);
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { success: false, error: error.message };
  }
};

// 获取体重趋势
async function getWeightTrend(openid, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const result = await db.collection('weight_records')
    .where({
      _openid: openid,
      date: db.command.gte(startDate.toISOString().split('T')[0])
    })
    .orderBy('date', 'asc')
    .get();

  return { success: true, data: result.data };
}

// 获取热量平衡分析
async function getCalorieBalance(openid, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  // 获取饮食记录
  const foodResult = await db.collection('food_records')
    .where({
      _openid: openid,
      date: db.command.gte(startDate.toISOString().split('T')[0])
    })
    .get();

  // 获取运动记录
  const sportResult = await db.collection('sport_records')
    .where({
      _openid: openid,
      date: db.command.gte(startDate.toISOString().split('T')[0])
    })
    .get();

  // 按日期分组计算
  const dailyData = {};
  
  // 处理饮食数据
  foodResult.data.forEach(record => {
    if (!dailyData[record.date]) {
      dailyData[record.date] = { intake: 0, burn: 0 };
    }
    
    if (record.foods && Array.isArray(record.foods)) {
      record.foods.forEach(food => {
        dailyData[record.date].intake += food.calories || 0;
      });
    }
  });

  // 处理运动数据
  sportResult.data.forEach(record => {
    if (!dailyData[record.date]) {
      dailyData[record.date] = { intake: 0, burn: 0 };
    }
    dailyData[record.date].burn += record.calories || 0;
  });

  // 转换为数组格式
  const data = Object.keys(dailyData).map(date => ({
    date,
    intake: dailyData[date].intake,
    burn: dailyData[date].burn,
    deficit: dailyData[date].burn - dailyData[date].intake
  }));

  return { success: true, data };
}

// 获取习惯分析
async function getHabitAnalysis(openid, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // 获取饮食记录
  const foodResult = await db.collection('food_records')
    .where({
      _openid: openid,
      date: db.command.gte(startDate.toISOString().split('T')[0])
    })
    .get();

  // 计算记录频率
  const recordDates = new Set();
  const highCalorieFoods = {};
  const hourlyPattern = new Array(24).fill(0);

  foodResult.data.forEach(record => {
    recordDates.add(record.date);
    
    if (record.foods && Array.isArray(record.foods)) {
      record.foods.forEach(food => {
        // 统计高热量食物
        if (food.calories > 200) {
          highCalorieFoods[food.name] = (highCalorieFoods[food.name] || 0) + 1;
        }
      });
    }

    // 统计时间模式（基于创建时间）
    if (record.createdAt) {
      const hour = new Date(record.createdAt).getHours();
      hourlyPattern[hour]++;
    }
  });

  // 计算记录频率
  const recordFrequency = Math.round((recordDates.size / days) * 100);

  // 获取最常见的高热量食物
  const topHighCalorieFoods = Object.entries(highCalorieFoods)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([name, count]) => ({ name, count }));

  // 找出高峰时段
  const peakHours = hourlyPattern
    .map((count, hour) => ({ hour, count }))
    .filter(item => item.count > 0)
    .sort((a, b) => b.count - a.count)
    .slice(0, 3)
    .map(item => item.hour);

  return {
    success: true,
    data: {
      recordFrequency,
      highCalorieFoods: topHighCalorieFoods,
      peakHours,
      weeklyPattern: [] // 可以进一步实现周模式分析
    }
  };
}

// 预测减重进度
async function predictWeightLoss(openid) {
  // 获取用户档案
  const userResult = await db.collection('users')
    .where({ _openid: openid })
    .get();

  if (userResult.data.length === 0) {
    return { success: false, error: '用户档案不存在' };
  }

  const user = userResult.data[0];
  const currentWeight = user.weight || 60;
  const targetWeight = user.targetWeight || 55;
  const weightLossRate = user.goals?.weightLossRate || 0.5;

  // 计算预计时间
  const totalWeightLoss = currentWeight - targetWeight;
  const estimatedWeeks = Math.ceil(totalWeightLoss / weightLossRate);
  
  // 计算目标日期
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + estimatedWeeks * 7);

  // 计算当前进度（这里需要获取最新体重记录）
  const weightResult = await db.collection('weight_records')
    .where({ _openid: openid })
    .orderBy('date', 'desc')
    .limit(1)
    .get();

  let currentProgress = 0;
  if (weightResult.data.length > 0) {
    const latestWeight = weightResult.data[0].weight;
    const weightLost = currentWeight - latestWeight;
    currentProgress = Math.round((weightLost / totalWeightLoss) * 100);
  }

  // 生成建议
  const recommendations = [
    {
      title: '保持热量缺口',
      content: '建议每日保持300-500kcal的热量缺口，有助于稳定减重。',
      icon: '🔥'
    },
    {
      title: '增加蛋白质摄入',
      content: '蛋白质有助于维持肌肉量，建议每公斤体重摄入1.2-1.6g蛋白质。',
      icon: '🥩'
    },
    {
      title: '规律运动',
      content: '建议每周进行3-5次有氧运动，每次30-45分钟。',
      icon: '🏃'
    }
  ];

  return {
    success: true,
    data: {
      estimatedWeeks,
      targetDate: targetDate.toISOString().split('T')[0],
      currentProgress: Math.max(0, Math.min(100, currentProgress)),
      recommendations
    }
  };
}
