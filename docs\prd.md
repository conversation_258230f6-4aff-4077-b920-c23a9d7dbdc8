# 智食派 - 体重控制管理小程序 PRD

**产品需求文档 (Product Requirements Document)**

---

## 1. 项目概述

### 1.1 产品信息
- **产品名称**：智食派
- **产品类型**：微信小程序

### 1.2 产品定位
智食派是一款基于科学热量平衡原理的专业体重控制管理工具，通过智能化的饮食记录、数据分析和个性化建议，帮助用户实现可持续的体重管理目标。

### 1.3 核心价值主张
- **科学性**：基于热量平衡原理（热量摄入 < 热量消耗 = 体重下降）
- **智能化**：AI食物识别、智能数据分析、个性化建议
- **专业性**：精准的营养数据、科学的减重预测、健康风险提醒

## 2. 用户画像与需求分析

### 2.1 目标用户
**主要用户**（70%）：有明确减重需求的人群
- 年龄：25-40岁
- BMI：>24
- 特征：有一定健康意识，愿意为体重管理投入时间

**次要用户**（30%）：体重维持/增重人群
- 健身爱好者
- 体重维持需求者
- 增重需求者

### 2.2 核心痛点
1. 不知道每天应该摄入多少热量
2. 食物热量计算复杂困难
3. 体重波动缺乏科学分析
4. 减肥进度难以量化追踪
5. 缺乏专业的减肥指导

## 3. 功能架构设计

### 3.1 信息架构
```
智食派小程序
├── 首页（智能仪表盘）
├── 饮食记录
├── 体重管理
├── 数据报告
├── 运动管理
└── 个人中心
```

### 3.2 核心功能模块

## 4. 详细功能需求

### 4.1 智能仪表盘（首页）

#### 4.1.1 个人档案卡片
**功能描述**：展示用户基础信息和关键健康指标

**具体需求**：
- 显示基础数据：身高、当前体重、目标体重、年龄、性别
- 计算并显示关键指标：
  - BMI值及对应健康状态
  - 基础代谢率(BMR)
  - 每日热量需求(TDEE)
- 进度展示：
  - 已减重量（当前体重-初始体重）
  - 目标完成度百分比
  - 预计达成时间

**技术实现要点**：
- BMR计算使用Mifflin-St Jeor公式
- TDEE = BMR × 活动系数（久坐1.2，轻度活动1.375，中度活动1.55，重度活动1.725）
- 数据实时更新，支持下拉刷新

#### 4.1.2 今日概览
**功能描述**：展示当日热量摄入消耗情况

**具体需求**：
- 热量预算显示：
  - 今日可摄入热量（基于减重目标计算）
  - 已摄入热量（来自饮食记录）
  - 剩余热量（可摄入-已摄入+运动消耗）
- 可视化进度条：
  - 绿色：剩余热量>200kcal（安全区）
  - 黄色：剩余热量0-200kcal（警告区）
  - 红色：剩余热量<0（超标区）
- 运动消耗：显示当日运动消耗热量总计
- 水分摄入：显示当日饮水量/目标饮水量

#### 4.1.3 快捷操作
**功能描述**：提供快速记录入口

**具体需求**：
- 记录饮食按钮 → 跳转饮食记录页
- 记录运动按钮 → 跳转运动记录页
- 记录体重按钮 → 弹出体重输入框
- 拍照识别按钮 → 调用相机进行食物识别

### 4.2 专业饮食记录

#### 4.2.1 多种记录方式
**功能描述**：提供多样化的饮食记录方式

**具体需求**：
1. **拍照识别**：
   - 调用微信相机API
   - 集成食物识别AI服务
   - 自动识别食物类型和估算分量
   - 支持手动调整识别结果

2. **语音记录**：
   - 调用微信语音识别API
   - 解析语音中的食物信息
   - 支持自然语言描述（如"吃了一碗米饭"）

3. **搜索添加**：
   - 提供食物搜索功能
   - 支持拼音搜索和模糊匹配
   - 显示搜索历史和常用食物

4. **扫码录入**：
   - 调用微信扫码API
   - 识别包装食品条码
   - 从商品数据库获取营养信息

#### 4.2.2 营养分析
**功能描述**：对记录的食物进行营养成分分析

**具体需求**：
- 三大营养素占比饼图：碳水化合物/蛋白质/脂肪
- 热量密度分析：每100g食物的热量值
- 饱腹感指数评分：1-5星评级
- 升糖指数(GI)标注：高/中/低GI标识

### 4.3 专业食物库

#### 4.3.1 食物分类与数据
**功能描述**：提供完整的食物营养数据库

**具体需求**：
- 食物分类：主食类、蛋白质类、蔬菜类、水果类、零食类、饮品类
- 每类食物按热量密度排序（从低到高）
- 详细营养信息：
  - 每100g：热量、蛋白质、碳水化合物、脂肪、纤维
  - 常见分量热量（1个苹果、1碗米饭、1片面包等）
  - 减肥友好度评级（1-5星）
  - 饱腹感指数、升糖指数

#### 4.3.2 智能推荐
**功能描述**：基于用户目标提供食物建议

**具体需求**：
- 低热量替代品推荐
- 相似食物热量对比
- 减肥期推荐/不推荐标签
- 基于用户历史记录的个性化推荐

### 4.4 热量体重日历

#### 4.4.1 日历视图
**功能描述**：以日历形式展示体重和热量数据

**具体需求**：
- 每日体重记录点显示
- 热量摄入/消耗对比
- 颜色编码：
  - 绿色：热量达标日
  - 红色：热量超标日
  - 灰色：未记录日
- 支持点击查看具体日期详情

#### 4.4.2 周/月视图
**功能描述**：展示中长期趋势数据

**具体需求**：
- 体重变化趋势线图
- 平均热量摄入统计
- 减重速度分析（健康减重标准：0.5-1kg/周）
- 支持切换时间范围（7天/30天/90天）

### 4.5 数据报告中心

#### 4.5.1 体重趋势分析
**功能描述**：深度分析体重变化规律

**具体需求**：
- 体重变化折线图（可选择时间范围）
- 减重速度分析：
  - 计算周平均减重速度
  - 判断是否在健康范围内（0.5-1kg/周）
  - 过快/过慢减重提醒
- 体重波动分析：
  - 区分真实减重vs水分波动
  - 基于移动平均线平滑波动

#### 4.5.2 热量平衡分析
**功能描述**：分析热量摄入与消耗平衡

**具体需求**：
- 每日热量摄入vs消耗对比图
- 热量缺口统计：
  - 理论热量缺口
  - 实际减重对比
  - 预测vs实际减重差异分析
- 营养素摄入占比饼图

#### 4.5.3 习惯分析报告
**功能描述**：分析用户饮食运动习惯

**具体需求**：
- 饮食记录频率统计
- 高热量食物摄入频次分析
- 最容易超标的时间段分析
- 基于历史数据的减肥效果预测

### 4.6 运动管理

#### 4.6.1 运动记录
**功能描述**：记录和计算运动消耗

**具体需求**：
- 常见运动类型数据库（跑步、游泳、健身等）
- 运动时长记录
- 基于体重和运动强度自动计算热量消耗
- 支持自定义运动类型

#### 4.6.2 运动推荐
**功能描述**：提供个性化运动建议

**具体需求**：
- 基于当前体重计算不同运动的热量消耗
- 适合减肥的运动类型推荐
- 运动强度建议（基于心率区间）
- 每日运动目标设定

### 4.7 个人中心

#### 4.7.1 基础档案管理
**功能描述**：管理用户基础信息

**具体需求**：
- 身体数据录入：身高、体重、年龄、性别
- 活动水平选择：久坐、轻度活动、中度活动、重度活动
- 目标设定：目标体重、期望减重速度、目标达成日期
- 健康状况：慢性疾病、过敏信息、特殊饮食需求

#### 4.7.2 减肥计划定制
**功能描述**：基于个人数据制定减肥方案

**具体需求**：
- 自动计算每日热量需求
- 制定个性化减肥方案
- 阶段性目标设定
- 计划调整建议

#### 4.7.3 设置与偏好
**功能描述**：个性化设置选项

**具体需求**：
- 提醒设置：体重记录提醒、饮食记录提醒
- 单位设置：公制/英制切换
- 隐私设置：数据分享权限
- 关于页面：版本信息、用户协议、隐私政策

## 5. 核心算法需求

### 5.1 热量需求计算算法
```javascript
// 基础代谢率计算（Mifflin-St Jeor公式）
function calculateBMR(weight, height, age, gender) {
  if (gender === 'male') {
    return 10 * weight + 6.25 * height - 5 * age + 5;
  } else {
    return 10 * weight + 6.25 * height - 5 * age - 161;
  }
}

// 每日总消耗计算
function calculateTDEE(bmr, activityLevel) {
  const activityMultipliers = {
    sedentary: 1.2,
    light: 1.375,
    moderate: 1.55,
    heavy: 1.725
  };
  return bmr * activityMultipliers[activityLevel];
}

// 减肥热量目标计算
function calculateWeightLossCalories(tdee, weightLossGoal) {
  // 每周减重0.5kg需要每日热量缺口约500kcal
  const dailyDeficit = weightLossGoal * 500;
  return tdee - dailyDeficit;
}
```

### 5.2 减重预测算法
```javascript
// 理论减重计算
function predictWeightLoss(calorieDeficit) {
  // 7700kcal ≈ 1kg脂肪
  return calorieDeficit / 7700;
}

// 预计达成时间计算
function estimateTimeToGoal(currentWeight, targetWeight, weeklyWeightLoss) {
  const totalWeightLoss = currentWeight - targetWeight;
  return Math.ceil(totalWeightLoss / weeklyWeightLoss);
}
```

### 5.3 健康风险评估
```javascript
// 减重速度健康检查
function checkWeightLossRate(weeklyWeightLoss) {
  if (weeklyWeightLoss > 1) {
    return { status: 'warning', message: '减重速度过快，建议降低热量缺口' };
  } else if (weeklyWeightLoss < 0.2) {
    return { status: 'info', message: '减重速度较慢，可适当增加热量缺口' };
  }
  return { status: 'success', message: '减重速度健康' };
}

// 热量摄入安全检查
function checkCalorieIntake(dailyCalories, bmr) {
  if (dailyCalories < bmr * 0.8) {
    return { status: 'danger', message: '热量摄入过低，可能影响基础代谢' };
  }
  return { status: 'safe' };
}
```