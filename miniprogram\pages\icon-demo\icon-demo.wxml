<!-- 图标展示页面 -->
<view class="page-container">
  <view class="container">
    <!-- 页面标题 -->
    <view class="text-center mb-6">
      <view class="text-2xl font-bold text-primary mb-2">专业图标系统</view>
      <view class="text-sm text-secondary">基于 Unicode 字符的图标库</view>
    </view>

    <!-- 阿里巴巴图标库 -->
    <view class="card mb-6">
      <view class="card-header">
        <view class="card-title">阿里巴巴图标库</view>
      </view>
      <view class="card-body">
        <view class="grid grid-cols-3 gap-4">
          <view class="icon-demo-item">
            <icon name="home" size="xl" color="primary"></icon>
            <view class="icon-name">home</view>
          </view>
          <view class="icon-demo-item">
            <icon name="profile" size="xl" color="primary"></icon>
            <view class="icon-name">profile</view>
          </view>
          <view class="icon-demo-item">
            <icon name="camera" size="xl" color="primary"></icon>
            <view class="icon-name">camera</view>
          </view>
          <view class="icon-demo-item">
            <icon name="weight" size="xl" color="success"></icon>
            <view class="icon-name">weight</view>
          </view>
          <view class="icon-demo-item">
            <icon name="food" size="xl" color="warning"></icon>
            <view class="icon-name">food</view>
          </view>
          <view class="icon-demo-item">
            <icon name="sport" size="xl" color="info"></icon>
            <view class="icon-name">sport</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="error"></icon>
            <view class="icon-name">fire</view>
          </view>
          <view class="icon-demo-item">
            <icon name="chart" size="xl" color="secondary"></icon>
            <view class="icon-name">chart</view>
          </view>
          <view class="icon-demo-item">
            <icon name="plus" size="xl" color="primary"></icon>
            <view class="icon-name">plus</view>
          </view>
        </view>
      </view>
    </view>



    <!-- 尺寸展示 -->
    <view class="card mb-6">
      <view class="card-header">
        <view class="card-title">尺寸规格</view>
      </view>
      <view class="card-body">
        <view class="flex items-center justify-around">
          <view class="icon-demo-item">
            <icon name="heart" size="xs" color="error"></icon>
            <view class="icon-name">xs</view>
          </view>
          <view class="icon-demo-item">
            <icon name="heart" size="sm" color="error"></icon>
            <view class="icon-name">sm</view>
          </view>
          <view class="icon-demo-item">
            <icon name="heart" size="md" color="error"></icon>
            <view class="icon-name">md</view>
          </view>
          <view class="icon-demo-item">
            <icon name="heart" size="lg" color="error"></icon>
            <view class="icon-name">lg</view>
          </view>
          <view class="icon-demo-item">
            <icon name="heart" size="xl" color="error"></icon>
            <view class="icon-name">xl</view>
          </view>
          <view class="icon-demo-item">
            <icon name="heart" size="xxl" color="error"></icon>
            <view class="icon-name">xxl</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 颜色展示 -->
    <view class="card mb-6">
      <view class="card-header">
        <view class="card-title">颜色变体</view>
      </view>
      <view class="card-body">
        <view class="grid grid-cols-3 gap-4">
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="primary"></icon>
            <view class="icon-name">primary</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="secondary"></icon>
            <view class="icon-name">secondary</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="success"></icon>
            <view class="icon-name">success</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="warning"></icon>
            <view class="icon-name">warning</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="error"></icon>
            <view class="icon-name">error</view>
          </view>
          <view class="icon-demo-item">
            <icon name="fire" size="xl" color="info"></icon>
            <view class="icon-name">info</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="text-center">
      <button class="btn btn-primary" bindtap="goBack">
        <icon name="home" size="sm" color="inverse" custom-style="margin-right: 8rpx;"></icon>
        返回首页
      </button>
    </view>
  </view>
</view>
