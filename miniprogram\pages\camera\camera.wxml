<!-- 智食派AI拍照识别页面 -->
<view class="page-container">
  <!-- 相机界面 -->
  <view wx:if="{{currentStep === 'camera'}}" class="camera-container">
    <!-- 相机预览 -->
    <camera 
      class="camera-preview"
      device-position="back"
      flash="off"
      binderror="onCameraError"
    >
      <!-- 识别框引导 -->
      <view class="camera-guide">
        <view class="guide-frame">
          <view class="guide-corner tl"></view>
          <view class="guide-corner tr"></view>
          <view class="guide-corner bl"></view>
          <view class="guide-corner br"></view>
        </view>
        <view class="guide-text">将食物放在框内</view>
      </view>
    </camera>

    <!-- 相机控制栏 -->
    <view class="camera-controls">
      <view class="control-item" bindtap="onChooseImage">
        <view class="control-icon">🖼️</view>
        <view class="control-text">相册</view>
      </view>
      
      <view class="capture-btn" bindtap="onTakePhoto">
        <view class="capture-inner"></view>
      </view>
      
      <view class="control-item" bindtap="onGoBack">
        <view class="control-icon">❌</view>
        <view class="control-text">取消</view>
      </view>
    </view>

    <!-- 目标餐次提示 -->
    <view class="meal-indicator">
      <text>添加到：{{mealName}}</text>
    </view>
  </view>

  <!-- 识别结果界面 -->
  <view wx:elif="{{currentStep === 'result'}}" class="result-container">
    <!-- 识别中状态 -->
    <view wx:if="{{recognizing}}" class="recognizing-state">
      <view class="recognizing-animation">🤖</view>
      <view class="recognizing-text">AI正在识别中...</view>
    </view>

    <!-- 识别结果 -->
    <view wx:else class="recognition-result">
      <!-- 拍摄的图片 -->
      <view class="result-image-container">
        <image class="result-image" src="{{imageUrl}}" mode="aspectFit" />
        <view class="retake-btn" bindtap="onRetakePhoto">重新拍照</view>
      </view>

      <!-- 识别的食物列表 -->
      <view class="recognized-foods">
        <view class="section-title">识别结果</view>
        
        <view wx:if="{{adjustedFoods.length === 0}}" class="no-result">
          <view class="no-result-text">未识别到食物</view>
          <view class="no-result-hint">请重新拍照或手动添加</view>
        </view>

        <view wx:else>
          <view 
            wx:for="{{adjustedFoods}}" 
            wx:key="index"
            class="food-result-item"
          >
            <view class="food-result-main">
              <view class="food-result-info">
                <view class="food-result-name">{{item.name}}</view>
                <view class="food-result-amount">{{item.amount}}{{item.unit}}</view>
              </view>
              
              <view class="food-result-nutrition">
                <view class="food-result-calories">{{item.calories}}kcal</view>
                <view class="food-result-macros">
                  蛋白质{{item.protein}}g | 碳水{{item.carbs}}g | 脂肪{{item.fat}}g
                </view>
              </view>
            </view>
            
            <view class="food-result-actions">
              <view 
                class="adjust-btn"
                data-index="{{index}}"
                bindtap="onAdjustAmount"
              >
                调整
              </view>
              <view 
                class="delete-btn"
                data-index="{{index}}"
                bindtap="onDeleteFood"
              >
                删除
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 智能建议 -->
      <view wx:if="{{recommendations.length > 0}}" class="recommendations">
        <view class="section-title">💡 智能建议</view>
        <view 
          wx:for="{{recommendations}}" 
          wx:key="index"
          class="recommendation-item"
          data-recommendation="{{item}}"
          bindtap="onViewRecommendation"
        >
          <view class="recommendation-icon">{{item.icon}}</view>
          <view class="recommendation-content">
            <view class="recommendation-title">{{item.title}}</view>
            <view class="recommendation-desc">{{item.description}}</view>
          </view>
          <view class="recommendation-arrow">></view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="result-actions">
        <view class="action-btn secondary" bindtap="onRetakePhoto">
          重新拍照
        </view>
        <view 
          class="action-btn primary {{adjustedFoods.length === 0 ? 'disabled' : ''}}"
          bindtap="onConfirmAdd"
        >
          确认添加
        </view>
      </view>
    </view>
  </view>
</view>
