<!-- 通用图标组件 -->
<view class="icon-container {{size}} {{color}}" style="{{customStyle}}">
  <!-- 健康相关图标 -->
  <view wx:if="{{name === 'heart'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
    </svg>
  </view>

  <!-- 食物相关图标 -->
  <view wx:elif="{{name === 'apple'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
    </svg>
  </view>

  <!-- 运动相关图标 -->
  <view wx:elif="{{name === 'fire'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.28 2.65-.2 3.73-.74 1.67-2.23 2.72-4.01 2.72z"/>
    </svg>
  </view>

  <!-- 数据相关图标 -->
  <view wx:elif="{{name === 'chart'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
    </svg>
  </view>

  <!-- 相机图标 -->
  <view wx:elif="{{name === 'camera'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 15.2l3.536-3.536 1.414 1.414L12 17.828 7.05 12.878l1.414-1.414L12 15.2zm0-6.4L8.464 5.264 7.05 6.678 12 11.628l4.95-4.95-1.414-1.414L12 8.8z"/>
      <circle cx="12" cy="12" r="3.2"/>
      <path d="M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z"/>
    </svg>
  </view>

  <!-- 体重秤图标 -->
  <view wx:elif="{{name === 'scale'}}" class="icon-svg">
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z"/>
      <path d="M11 7h2v6h-2zm0 8h2v2h-2z"/>
    </svg>
  </view>

  <!-- 默认显示文本图标 -->
  <text wx:else class="icon-text">{{name}}</text>
</view>
