<!-- 专业图标组件 - 使用阿里巴巴图标库 -->
<view class="icon-container {{size}} {{color}}" style="{{customStyle}}">

  <!-- 个人档案图标 -->
  <view wx:if="{{name === 'user' || name === 'profile'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M864 920c0 22.064-17.936 40-40 40h-608a40.032 40.032 0 0 1-40-40v-800C176 97.936 193.936 80 216 80h608C846.064 80 864 97.936 864 120v800zM824 32h-608A88.112 88.112 0 0 0 128 120v800c0 48.512 39.488 88 88 88h608c48.512 0 88-39.488 88-88v-800C912 71.488 872.512 32 824 32z m-464 619.312h192a24 24 0 0 0 0-48h-192a24 24 0 0 0 0 48m0 108.688h256a24 24 0 0 0 0-48h-256a24 24 0 0 0 0 48m320 64h-320a24 24 0 0 0 0 48h320a24 24 0 0 0 0-48M423.696 510.288C375.232 492.432 368 463.6 368 448c0-52.416 26.192-89.488 73.92-107.52A103.472 103.472 0 0 1 416 272c0-57.344 46.656-104 104-104S624 214.656 624 272c0 26.24-9.84 50.16-25.92 68.48C645.808 358.512 672 395.584 672 448c0 56.848-81.872 77.008-152 77.008-36.304 0-70.48-5.232-96.304-14.72zM520 376c-69.008 0-104 24.224-104 72 0 9.84 36.704 29.008 104 29.008 67.296 0 104-19.168 104-29.008 0-47.776-34.992-72-104-72zM464 272c0 30.88 25.12 56 56 56S576 302.88 576 272s-25.12-56-56-56S464 241.12 464 272z"/>
    </svg>
  </view>

  <!-- 体重图标 -->
  <view wx:elif="{{name === 'scale' || name === 'weight'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
      <path d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0z"/>
      <path d="M488 424h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V432c0-4.4 3.6-8 8-8z"/>
    </svg>
  </view>

  <!-- 拍照图标 -->
  <view wx:elif="{{name === 'camera'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M864 248H728l-32.4-90.8a32.07 32.07 0 0 0-30.2-21.2H358.6c-13.5 0-25.6 8.5-30.1 21.2L296 248H160c-44.2 0-80 35.8-80 80v456c0 44.2 35.8 80 80 80h704c44.2 0 80-35.8 80-80V328c0-44.2-35.8-80-80-80zM512 716c-88.4 0-160-71.6-160-160s71.6-160 160-160 160 71.6 160 160-71.6 160-160 160zm0-256c-53 0-96 43-96 96s43 96 96 96 96-43 96-96-43-96-96-96z"/>
    </svg>
  </view>

  <!-- 饮食图标 -->
  <view wx:elif="{{name === 'food' || name === 'utensils'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M128 352l192 320h384l192-320H128z m64 64h576l-128 192H320l-128-192z"/>
      <path d="M320 256h384v64H320z"/>
      <path d="M384 192h256v64H384z"/>
    </svg>
  </view>

  <!-- 运动图标 -->
  <view wx:elif="{{name === 'sport' || name === 'dumbbell'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM368 744c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v464zm384 0c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v464zM512 472c-4.4 0-8-3.6-8-8v-80c0-4.4 3.6-8 8-8s8 3.6 8 8v80c0 4.4-3.6 8-8 8zm0 160c-4.4 0-8-3.6-8-8v-80c0-4.4 3.6-8 8-8s8 3.6 8 8v80c0 4.4-3.6 8-8 8z"/>
    </svg>
  </view>

  <!-- 热量图标 -->
  <view wx:elif="{{name === 'fire' || name === 'calorie'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M834.1 469.2A347.49 347.49 0 0 0 751.2 354l-29.1-26.7a8.09 8.09 0 0 0-13.5 2.7l-13.4 21.3c-32.1 51.1-81.1 83.3-135.7 89.4a8.1 8.1 0 0 0-6.8 6.8c-2.7 10.1-8.4 18.8-16.2 24.2l-30.3 21.2a8.1 8.1 0 0 0-2.2 11.2l9.7 12.4a8.03 8.03 0 0 0 10.9 2.9c18.6-8.8 39.4-13.2 60.5-12.9 126.2 1.9 228.6 102.6 228.6 228.8 0 126.2-102.4 228.8-228.6 228.8s-228.6-102.6-228.6-228.8c0-79.5 40.5-149.6 102.1-191.4a8.13 8.13 0 0 0 3.2-8.9l-4.3-17.3a8.3 8.3 0 0 0-10.6-5.9A347.17 347.17 0 0 0 189.9 469.2c-2.2 2.7-2.6 6.5-1.2 9.6 24.6 54.6 75.7 91.2 135.1 91.2 18.4 0 36.2-3.6 52.5-10.1 2.4-1 4.1-3.2 4.1-5.8 0-2.6-1.7-4.8-4.1-5.8-16.3-6.5-34.1-10.1-52.5-10.1-59.4 0-110.5-36.6-135.1-91.2-1.4-3.1-1-6.9 1.2-9.6z"/>
    </svg>
  </view>

  <!-- 趋势/报告图标 -->
  <view wx:elif="{{name === 'chart' || name === 'report' || name === 'trend'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"/>
      <path d="M305.8 637.7c3.1 3.1 8.1 3.1 11.3 0l138.3-137.6L583 628.5c3.1 3.1 8.2 3.1 11.3 0l275.4-275.3c3.1-3.1 3.1-8.2 0-11.3l-39.6-39.6c-3.1-3.1-8.2-3.1-11.3 0l-230 229.1L461.4 404c-3.1-3.1-8.2-3.1-11.3 0L266.3 586.7c-3.1 3.1-3.1 8.2 0 11.3l39.5 39.7z"/>
    </svg>
  </view>

  <!-- 首页图标 -->
  <view wx:elif="{{name === 'home'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 0 0-44.4 0L77.5 505a63.9 63.9 0 0 0-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0 0 18.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"/>
    </svg>
  </view>

  <!-- 功能类图标 -->
  <view wx:elif="{{name === 'plus'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"/>
      <path d="M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>
    </svg>
  </view>

  <view wx:elif="{{name === 'check'}}" class="icon-svg">
    <svg viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"/>
    </svg>
  </view>

  <!-- 默认显示文本图标 -->
  <text wx:else class="icon-text">{{name}}</text>
</view>
