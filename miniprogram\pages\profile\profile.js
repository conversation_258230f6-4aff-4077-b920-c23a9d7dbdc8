// 智食派个人中心页面
const { userAPI, showLoading, hideLoading, showSuccess, showError } = require('../../utils/api');
const { calculateBMI, getBMIStatus, calculateBMR, calculateTDEE, calculateWeightLossCalories } = require('../../utils/calculator');
const { GENDERS, GENDER_NAMES, ACTIVITY_LEVELS, ACTIVITY_NAMES, ACTIVITY_DESCRIPTIONS, DEFAULT_USER_PROFILE, LIMITS } = require('../../utils/constants');

Page({
  data: {
    // 用户档案
    userProfile: { ...DEFAULT_USER_PROFILE },
    
    // 计算结果
    bmi: 0,
    bmiStatus: {},
    bmr: 0,
    tdee: 0,
    targetCalories: 0,
    
    // 表单选项
    genderOptions: [
      { value: GENDERS.FEMALE, label: GENDER_NAMES[GENDERS.FEMALE] },
      { value: GENDERS.MALE, label: GENDER_NAMES[GENDERS.MALE] }
    ],
    activityOptions: [
      { value: ACTIVITY_LEVELS.SEDENTARY, label: ACTIVITY_NAMES[ACTIVITY_LEVELS.SEDENTARY], desc: ACTIVITY_DESCRIPTIONS[ACTIVITY_LEVELS.SEDENTARY] },
      { value: ACTIVITY_LEVELS.LIGHT, label: ACTIVITY_NAMES[ACTIVITY_LEVELS.LIGHT], desc: ACTIVITY_DESCRIPTIONS[ACTIVITY_LEVELS.LIGHT] },
      { value: ACTIVITY_LEVELS.MODERATE, label: ACTIVITY_NAMES[ACTIVITY_LEVELS.MODERATE], desc: ACTIVITY_DESCRIPTIONS[ACTIVITY_LEVELS.MODERATE] },
      { value: ACTIVITY_LEVELS.HEAVY, label: ACTIVITY_NAMES[ACTIVITY_LEVELS.HEAVY], desc: ACTIVITY_DESCRIPTIONS[ACTIVITY_LEVELS.HEAVY] }
    ],
    
    // 表单状态
    editing: false,
    hasProfile: false,
    
    // 加载状态
    loading: true,
    saving: false
  },

  onLoad() {
    this.loadUserProfile();
  },

  onShow() {
    if (!this.data.loading) {
      this.loadUserProfile();
    }
  },

  // 加载用户档案
  async loadUserProfile() {
    try {
      showLoading('加载中...');
      const profile = await userAPI.getUserProfile();
      
      this.setData({
        userProfile: { ...DEFAULT_USER_PROFILE, ...profile },
        hasProfile: true,
        editing: false
      });
      
      this.calculateMetrics();
    } catch (error) {
      console.error('加载用户档案失败:', error);
      // 如果没有档案，进入编辑模式
      this.setData({
        hasProfile: false,
        editing: true
      });
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 计算各项指标
  calculateMetrics() {
    const { height, weight, age, gender, activityLevel, targetWeight } = this.data.userProfile;
    
    if (height && weight && age && gender) {
      const bmi = calculateBMI(weight, height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(weight, height, age, gender);
      const tdee = calculateTDEE(bmr, activityLevel);
      
      // 计算目标热量（如果有目标体重）
      let targetCalories = tdee;
      if (targetWeight && targetWeight < weight) {
        const weightLossRate = 0.5; // 默认每周减重0.5kg
        targetCalories = calculateWeightLossCalories(tdee, weightLossRate);
      }
      
      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    }
  },

  // 开始编辑
  onStartEdit() {
    this.setData({ editing: true });
  },

  // 取消编辑
  onCancelEdit() {
    if (this.data.hasProfile) {
      this.setData({ editing: false });
      this.loadUserProfile();
    } else {
      // 如果没有档案，不能取消编辑
      wx.showModal({
        title: '提示',
        content: '请先完善个人信息',
        showCancel: false
      });
    }
  },

  // 保存档案
  async onSaveProfile() {
    if (!this.validateProfile()) {
      return;
    }

    try {
      this.setData({ saving: true });
      showLoading('保存中...');
      
      await userAPI.updateUserProfile(this.data.userProfile);
      
      this.setData({
        hasProfile: true,
        editing: false
      });
      
      this.calculateMetrics();
      showSuccess('保存成功');
      
    } catch (error) {
      console.error('保存用户档案失败:', error);
      showError('保存失败');
    } finally {
      this.setData({ saving: false });
      hideLoading();
    }
  },

  // 验证档案数据
  validateProfile() {
    const { height, weight, age, targetWeight } = this.data.userProfile;
    
    if (!height || height < LIMITS.MIN_HEIGHT || height > LIMITS.MAX_HEIGHT) {
      showError(`身高应在${LIMITS.MIN_HEIGHT}-${LIMITS.MAX_HEIGHT}cm之间`);
      return false;
    }
    
    if (!weight || weight < LIMITS.MIN_WEIGHT || weight > LIMITS.MAX_WEIGHT) {
      showError(`体重应在${LIMITS.MIN_WEIGHT}-${LIMITS.MAX_WEIGHT}kg之间`);
      return false;
    }
    
    if (!age || age < LIMITS.MIN_AGE || age > LIMITS.MAX_AGE) {
      showError(`年龄应在${LIMITS.MIN_AGE}-${LIMITS.MAX_AGE}岁之间`);
      return false;
    }
    
    if (targetWeight && (targetWeight < LIMITS.MIN_WEIGHT || targetWeight > LIMITS.MAX_WEIGHT)) {
      showError(`目标体重应在${LIMITS.MIN_WEIGHT}-${LIMITS.MAX_WEIGHT}kg之间`);
      return false;
    }
    
    if (targetWeight && targetWeight >= weight) {
      showError('目标体重应小于当前体重');
      return false;
    }
    
    return true;
  },

  // 输入处理
  onHeightInput(e) {
    const height = parseFloat(e.detail.value) || 0;
    this.setData({
      'userProfile.height': height
    });
  },

  onWeightInput(e) {
    const weight = parseFloat(e.detail.value) || 0;
    this.setData({
      'userProfile.weight': weight
    });
  },

  onAgeInput(e) {
    const age = parseInt(e.detail.value) || 0;
    this.setData({
      'userProfile.age': age
    });
  },

  onTargetWeightInput(e) {
    const targetWeight = parseFloat(e.detail.value) || 0;
    this.setData({
      'userProfile.targetWeight': targetWeight
    });
  },

  // 选择器处理
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    const gender = this.data.genderOptions[index].value;
    this.setData({
      'userProfile.gender': gender
    });
  },

  onActivityLevelChange(e) {
    const index = parseInt(e.detail.value);
    const activityLevel = this.data.activityOptions[index].value;
    this.setData({
      'userProfile.activityLevel': activityLevel
    });
  },

  // 查看体重记录
  onViewWeightRecords() {
    wx.navigateTo({
      url: '/pages/weight/weight'
    });
  },

  // 设置提醒
  onSetReminder() {
    wx.showToast({
      title: '提醒功能开发中',
      icon: 'none'
    });
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于智食派',
      content: '智食派是一款基于科学热量平衡原理的专业体重控制管理小程序，帮助用户实现可持续的体重管理目标。',
      showCancel: false
    });
  },

  // 意见反馈
  onFeedback() {
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    });
  }
});
