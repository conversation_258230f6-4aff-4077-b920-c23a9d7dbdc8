// 智食派常量定义

// 餐次类型
const MEAL_TYPES = {
  BREAKFAST: 'breakfast',
  LUNCH: 'lunch',
  DINNER: 'dinner',
  SNACK: 'snack'
};

// 餐次显示名称
const MEAL_NAMES = {
  [MEAL_TYPES.BREAKFAST]: '早餐',
  [MEAL_TYPES.LUNCH]: '午餐',
  [MEAL_TYPES.DINNER]: '晚餐',
  [MEAL_TYPES.SNACK]: '加餐'
};

// 餐次图标
const MEAL_ICONS = {
  [MEAL_TYPES.BREAKFAST]: '☀️',
  [MEAL_TYPES.LUNCH]: '🌞',
  [MEAL_TYPES.DINNER]: '🌙',
  [MEAL_TYPES.SNACK]: '🍎'
};

// 活动水平
const ACTIVITY_LEVELS = {
  SEDENTARY: 'sedentary',
  LIGHT: 'light',
  MODERATE: 'moderate',
  HEAVY: 'heavy'
};

// 活动水平显示名称
const ACTIVITY_NAMES = {
  [ACTIVITY_LEVELS.SEDENTARY]: '久坐少动',
  [ACTIVITY_LEVELS.LIGHT]: '轻度活动',
  [ACTIVITY_LEVELS.MODERATE]: '中度活动',
  [ACTIVITY_LEVELS.HEAVY]: '重度活动'
};

// 活动水平描述
const ACTIVITY_DESCRIPTIONS = {
  [ACTIVITY_LEVELS.SEDENTARY]: '办公室工作，很少运动',
  [ACTIVITY_LEVELS.LIGHT]: '轻度运动，每周1-3次',
  [ACTIVITY_LEVELS.MODERATE]: '中度运动，每周3-5次',
  [ACTIVITY_LEVELS.HEAVY]: '重度运动，每周6-7次'
};

// 性别
const GENDERS = {
  MALE: 'male',
  FEMALE: 'female'
};

// 性别显示名称
const GENDER_NAMES = {
  [GENDERS.MALE]: '男',
  [GENDERS.FEMALE]: '女'
};

// BMI状态
const BMI_STATUS = {
  UNDERWEIGHT: 'underweight',
  NORMAL: 'normal',
  OVERWEIGHT: 'overweight',
  OBESE: 'obese'
};

// BMI状态显示名称
const BMI_STATUS_NAMES = {
  [BMI_STATUS.UNDERWEIGHT]: '偏瘦',
  [BMI_STATUS.NORMAL]: '正常',
  [BMI_STATUS.OVERWEIGHT]: '超重',
  [BMI_STATUS.OBESE]: '肥胖'
};

// 记录类型
const RECORD_TYPES = {
  MANUAL: 'manual',
  CAMERA: 'camera',
  VOICE: 'voice',
  BARCODE: 'barcode'
};

// 记录类型显示名称
const RECORD_TYPE_NAMES = {
  [RECORD_TYPES.MANUAL]: '手动添加',
  [RECORD_TYPES.CAMERA]: '拍照识别',
  [RECORD_TYPES.VOICE]: '语音记录',
  [RECORD_TYPES.BARCODE]: '扫码录入'
};

// 升糖指数等级
const GI_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

// 升糖指数显示名称
const GI_LEVEL_NAMES = {
  [GI_LEVELS.LOW]: '低GI',
  [GI_LEVELS.MEDIUM]: '中GI',
  [GI_LEVELS.HIGH]: '高GI'
};

// 升糖指数颜色
const GI_LEVEL_COLORS = {
  [GI_LEVELS.LOW]: '#10b981',
  [GI_LEVELS.MEDIUM]: '#f59e0b',
  [GI_LEVELS.HIGH]: '#ef4444'
};

// 食物分类
const FOOD_CATEGORIES = {
  STAPLE: 'staple',
  PROTEIN: 'protein',
  VEGETABLE: 'vegetable',
  FRUIT: 'fruit',
  SNACK: 'snack',
  DRINK: 'drink'
};

// 食物分类显示名称
const FOOD_CATEGORY_NAMES = {
  [FOOD_CATEGORIES.STAPLE]: '主食类',
  [FOOD_CATEGORIES.PROTEIN]: '蛋白质类',
  [FOOD_CATEGORIES.VEGETABLE]: '蔬菜类',
  [FOOD_CATEGORIES.FRUIT]: '水果类',
  [FOOD_CATEGORIES.SNACK]: '零食类',
  [FOOD_CATEGORIES.DRINK]: '饮品类'
};

// 运动类型
const SPORT_TYPES = {
  RUNNING: 'running',
  WALKING: 'walking',
  CYCLING: 'cycling',
  SWIMMING: 'swimming',
  FITNESS: 'fitness',
  YOGA: 'yoga',
  BASKETBALL: 'basketball',
  FOOTBALL: 'football',
  BADMINTON: 'badminton',
  TENNIS: 'tennis'
};

// 运动类型显示名称
const SPORT_TYPE_NAMES = {
  [SPORT_TYPES.RUNNING]: '跑步',
  [SPORT_TYPES.WALKING]: '步行',
  [SPORT_TYPES.CYCLING]: '骑行',
  [SPORT_TYPES.SWIMMING]: '游泳',
  [SPORT_TYPES.FITNESS]: '健身',
  [SPORT_TYPES.YOGA]: '瑜伽',
  [SPORT_TYPES.BASKETBALL]: '篮球',
  [SPORT_TYPES.FOOTBALL]: '足球',
  [SPORT_TYPES.BADMINTON]: '羽毛球',
  [SPORT_TYPES.TENNIS]: '网球'
};

// 运动强度系数 (MET值)
const SPORT_MET_VALUES = {
  [SPORT_TYPES.RUNNING]: 8.0,
  [SPORT_TYPES.WALKING]: 3.5,
  [SPORT_TYPES.CYCLING]: 6.0,
  [SPORT_TYPES.SWIMMING]: 7.0,
  [SPORT_TYPES.FITNESS]: 5.0,
  [SPORT_TYPES.YOGA]: 2.5,
  [SPORT_TYPES.BASKETBALL]: 6.5,
  [SPORT_TYPES.FOOTBALL]: 7.0,
  [SPORT_TYPES.BADMINTON]: 5.5,
  [SPORT_TYPES.TENNIS]: 6.0
};

// 时间范围
const TIME_RANGES = {
  WEEK: 7,
  MONTH: 30,
  QUARTER: 90
};

// 时间范围显示名称
const TIME_RANGE_NAMES = {
  [TIME_RANGES.WEEK]: '7天',
  [TIME_RANGES.MONTH]: '30天',
  [TIME_RANGES.QUARTER]: '90天'
};

// 颜色主题
const COLORS = {
  PRIMARY: '#2563eb',
  SUCCESS: '#10b981',
  WARNING: '#f59e0b',
  DANGER: '#ef4444',
  INFO: '#3b82f6',
  SECONDARY: '#64748b',
  LIGHT: '#f8fafc',
  DARK: '#1e293b'
};

// 营养素颜色
const NUTRIENT_COLORS = {
  PROTEIN: '#3b82f6',
  CARBS: '#ef4444',
  FAT: '#f59e0b',
  FIBER: '#10b981'
};

// 默认用户档案
const DEFAULT_USER_PROFILE = {
  height: 165,
  weight: 60,
  targetWeight: 55,
  age: 25,
  gender: GENDERS.FEMALE,
  activityLevel: ACTIVITY_LEVELS.LIGHT
};

// 默认目标设置
const DEFAULT_GOALS = {
  weightLossRate: 0.5,
  targetDate: null,
  dailyCalories: 1500
};

// 最小最大值限制
const LIMITS = {
  MIN_HEIGHT: 100,
  MAX_HEIGHT: 250,
  MIN_WEIGHT: 30,
  MAX_WEIGHT: 200,
  MIN_AGE: 10,
  MAX_AGE: 100,
  MIN_CALORIES: 800,
  MAX_CALORIES: 5000,
  MIN_WEIGHT_LOSS_RATE: 0.1,
  MAX_WEIGHT_LOSS_RATE: 2.0
};

module.exports = {
  MEAL_TYPES,
  MEAL_NAMES,
  MEAL_ICONS,
  ACTIVITY_LEVELS,
  ACTIVITY_NAMES,
  ACTIVITY_DESCRIPTIONS,
  GENDERS,
  GENDER_NAMES,
  BMI_STATUS,
  BMI_STATUS_NAMES,
  RECORD_TYPES,
  RECORD_TYPE_NAMES,
  GI_LEVELS,
  GI_LEVEL_NAMES,
  GI_LEVEL_COLORS,
  FOOD_CATEGORIES,
  FOOD_CATEGORY_NAMES,
  SPORT_TYPES,
  SPORT_TYPE_NAMES,
  SPORT_MET_VALUES,
  TIME_RANGES,
  TIME_RANGE_NAMES,
  COLORS,
  NUTRIENT_COLORS,
  DEFAULT_USER_PROFILE,
  DEFAULT_GOALS,
  LIMITS
};
