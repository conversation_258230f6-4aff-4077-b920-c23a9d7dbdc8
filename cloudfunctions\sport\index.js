// 运动管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action } = event;

  try {
    switch (action) {
      case 'addSportRecord':
        return await addSportRecord(wxContext.OPENID, event.sportType, event.duration, event.calories, event.date);
      case 'getSportRecords':
        return await getSportRecords(wxContext.OPENID, event.date);
      case 'getSportTypes':
        return await getSportTypes();
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { success: false, error: error.message };
  }
};

// 添加运动记录
async function addSportRecord(openid, sportType, duration, calories, date) {
  await db.collection('sport_records').add({
    data: {
      _openid: openid,
      sportType: sportType,
      duration: duration,
      calories: calories,
      date: date,
      createdAt: new Date()
    }
  });

  return { success: true, data: { sportType, duration, calories, date } };
}

// 获取运动记录
async function getSportRecords(openid, date) {
  const result = await db.collection('sport_records')
    .where({
      _openid: openid,
      date: date
    })
    .orderBy('createdAt', 'desc')
    .get();

  return { success: true, data: result.data };
}

// 获取运动类型列表
async function getSportTypes() {
  const sportTypes = [
    { type: 'running', name: '跑步', metValue: 8.0, icon: '🏃' },
    { type: 'walking', name: '步行', metValue: 3.5, icon: '🚶' },
    { type: 'cycling', name: '骑行', metValue: 6.0, icon: '🚴' },
    { type: 'swimming', name: '游泳', metValue: 7.0, icon: '🏊' },
    { type: 'fitness', name: '健身', metValue: 5.0, icon: '💪' },
    { type: 'yoga', name: '瑜伽', metValue: 2.5, icon: '🧘' },
    { type: 'basketball', name: '篮球', metValue: 6.5, icon: '🏀' },
    { type: 'football', name: '足球', metValue: 7.0, icon: '⚽' },
    { type: 'badminton', name: '羽毛球', metValue: 5.5, icon: '🏸' },
    { type: 'tennis', name: '网球', metValue: 6.0, icon: '🎾' }
  ];

  return { success: true, data: sportTypes };
}
