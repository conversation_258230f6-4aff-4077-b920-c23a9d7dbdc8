<!-- 智食派个人中心页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 用户头像和基本信息 -->
    <view class="profile-header">
      <view class="avatar-container">
        <view class="avatar">👤</view>
      </view>
      <view class="user-info">
        <view class="username">智食派用户</view>
        <view class="user-status">{{hasProfile ? '已完善档案' : '请完善档案'}}</view>
      </view>
    </view>

    <!-- 基础指标 -->
    <view class="metrics-card">
      <view class="metrics-grid">
        <view class="metric-item">
          <view class="metric-value">{{bmi}}</view>
          <view class="metric-label">BMI</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{bmr}}</view>
          <view class="metric-label">基础代谢</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{targetCalories}}</view>
          <view class="metric-label">目标热量</view>
        </view>
      </view>
    </view>

    <!-- 个人档案 -->
    <view class="card">
      <view class="card-title">
        <text class="icon">📋</text>
        个人档案
        <view class="edit-btn" bindtap="{{editing ? 'onSaveProfile' : 'onStartEdit'}}">
          {{editing ? '保存' : '编辑'}}
        </view>
      </view>

      <view wx:if="{{editing}}" class="form-container">
        <!-- 编辑模式 -->
        <view class="form-item">
          <view class="form-label">身高 (cm)</view>
          <input
            class="form-input"
            type="number"
            value="{{userProfile.height}}"
            bindinput="onHeightInput"
            placeholder="请输入身高"
          />
        </view>

        <view class="form-item">
          <view class="form-label">体重 (kg)</view>
          <input
            class="form-input"
            type="digit"
            value="{{userProfile.weight}}"
            bindinput="onWeightInput"
            placeholder="请输入体重"
          />
        </view>

        <view class="form-item">
          <view class="form-label">年龄</view>
          <input
            class="form-input"
            type="number"
            value="{{userProfile.age}}"
            bindinput="onAgeInput"
            placeholder="请输入年龄"
          />
        </view>

        <view class="form-item">
          <view class="form-label">目标体重 (kg)</view>
          <input
            class="form-input"
            type="digit"
            value="{{userProfile.targetWeight}}"
            bindinput="onTargetWeightInput"
            placeholder="请输入目标体重"
          />
        </view>

        <view class="form-item">
          <view class="form-label">性别</view>
          <picker
            range="{{genderOptions}}"
            range-key="label"
            bindchange="onGenderChange"
          >
            <view class="picker-display">
              {{genderOptions[0].label}}
            </view>
          </picker>
        </view>

        <view class="form-actions">
          <view class="form-btn cancel" bindtap="onCancelEdit">取消</view>
          <view class="form-btn save" bindtap="onSaveProfile">保存</view>
        </view>
      </view>

      <view wx:else class="profile-display">
        <!-- 显示模式 -->
        <view class="profile-grid">
          <view class="profile-item">
            <view class="profile-label">身高</view>
            <view class="profile-value">{{userProfile.height}}cm</view>
          </view>
          <view class="profile-item">
            <view class="profile-label">体重</view>
            <view class="profile-value">{{userProfile.weight}}kg</view>
          </view>
          <view class="profile-item">
            <view class="profile-label">年龄</view>
            <view class="profile-value">{{userProfile.age}}岁</view>
          </view>
          <view class="profile-item">
            <view class="profile-label">目标体重</view>
            <view class="profile-value">{{userProfile.targetWeight}}kg</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-list">
      <view class="menu-item" bindtap="onViewWeightRecords">
        <view class="menu-icon">📊</view>
        <view class="menu-text">体重记录</view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="onSetReminder">
        <view class="menu-icon">⏰</view>
        <view class="menu-text">提醒设置</view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="onAbout">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">></view>
      </view>

      <view class="menu-item" bindtap="onFeedback">
        <view class="menu-icon">💬</view>
        <view class="menu-text">意见反馈</view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>
</view>