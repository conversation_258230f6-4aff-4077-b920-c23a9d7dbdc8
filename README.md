# 智食派 - 专业体重控制管理小程序

智食派是一款基于科学热量平衡原理的专业体重控制管理微信小程序，通过智能化的饮食记录、数据分析和个性化建议，帮助用户实现可持续的体重管理目标。

## 🎯 核心功能

### 📊 智能仪表盘
- 个人档案展示（身高、体重、BMI、基础代谢率）
- 今日热量摄入与消耗概览
- 营养素分布分析（蛋白质、碳水化合物、脂肪）
- 快捷操作入口（拍照识别、记录体重、饮食、运动）

### 🍽️ 专业饮食记录
- 多种记录方式：拍照识别、语音记录、搜索添加、扫码录入
- 餐次管理：早餐、午餐、晚餐、加餐
- 营养成分分析：热量、蛋白质、碳水化合物、脂肪、纤维
- 食物搜索和历史记录

### 📷 AI拍照识别
- 智能食物识别
- 营养成分自动计算
- 分量调整功能
- 智能建议和搭配推荐

### 📈 数据报告分析
- 体重趋势分析（7天/30天/90天）
- 热量平衡分析
- 习惯分析和改进建议
- 减重预测和目标追踪

### 🏃 运动管理
- 多种运动类型支持
- 热量消耗自动计算
- 运动推荐和目标设定
- 运动历史记录

### 👤 个人中心
- 基础档案管理
- 目标设定和计划制定
- 设置和偏好配置

## 🏗️ 技术架构

### 前端技术栈
- **框架**: 微信小程序原生框架
- **基础库**: latest
- **UI组件**: 微信小程序原生组件 + 自定义组件
- **状态管理**: 小程序原生数据绑定 + 全局数据管理
- **样式**: WXSS + 响应式设计

### 后端技术栈
- **云平台**: 腾讯云 CloudBase
- **云函数**: Node.js 18.15
- **数据库**: CloudBase 云数据库（MongoDB）
- **存储**: CloudBase 云存储
- **AI服务**: 第三方食物识别API

## 📁 项目结构

```
zhishipai/
├── miniprogram/                 # 小程序前端代码
│   ├── pages/                   # 页面目录
│   │   ├── index/              # 首页（智能仪表盘）
│   │   ├── food/               # 饮食记录
│   │   ├── camera/             # AI拍照识别
│   │   ├── report/             # 数据报告
│   │   ├── sport/              # 运动管理
│   │   └── profile/            # 个人中心
│   ├── utils/                  # 工具函数
│   │   ├── api.js              # API封装
│   │   ├── calculator.js       # 算法计算
│   │   └── constants.js        # 常量定义
│   ├── images/                 # 图片资源
│   ├── app.js                  # 小程序入口
│   ├── app.json               # 小程序配置
│   └── app.wxss               # 全局样式
├── cloudfunctions/             # 云函数目录
│   ├── user/                   # 用户管理云函数
│   ├── food/                   # 饮食相关云函数
│   ├── sport/                  # 运动相关云函数
│   ├── analysis/               # 数据分析云函数
│   └── ai/                     # AI识别云函数
├── specs/                      # 项目规范文档
│   └── zhishipai/
│       ├── requirements.md     # 需求文档
│       ├── design.md          # 技术方案设计
│       └── tasks.md           # 任务拆分
├── project.config.json         # 项目配置
├── cloudbaserc.json           # 云开发配置
└── README.md                  # 项目说明
```

## 🗄️ 数据库设计

### users 集合（用户信息）
- 用户基础信息：身高、体重、年龄、性别、活动水平
- 目标设置：目标体重、减重速度、目标日期
- 计算结果：每日热量目标

### food_records 集合（饮食记录）
- 记录信息：日期、餐次、食物列表
- 营养数据：热量、蛋白质、碳水化合物、脂肪、纤维
- 记录方式：手动、拍照、语音、扫码

### weight_records 集合（体重记录）
- 体重数据：体重值、记录日期
- 趋势分析：体重变化、减重速度

### sport_records 集合（运动记录）
- 运动信息：运动类型、时长、热量消耗
- 记录日期和时间

### food_library 集合（食物库）
- 食物信息：名称、分类、营养成分
- 扩展信息：升糖指数、饱腹感指数、减肥友好度

## 🔧 开发环境搭建

### 1. 环境要求
- Node.js 16+
- 微信开发者工具
- 腾讯云 CloudBase 账号

### 2. 安装依赖
```bash
# 安装云开发CLI
npm install -g @cloudbase/cli

# 登录云开发
tcb login
```

### 3. 配置云开发环境
1. 在 `cloudbaserc.json` 中配置环境ID
2. 创建云数据库集合
3. 配置数据库权限
4. 部署云函数

### 4. 小程序配置
1. 在微信开发者工具中导入项目
2. 配置 `project.config.json` 中的 appid
3. 开启云开发功能

## 🚀 部署说明

### 1. 云函数部署
```bash
# 部署所有云函数
tcb functions:deploy

# 部署单个云函数
tcb functions:deploy user
```

### 2. 数据库初始化
```bash
# 创建数据库集合
tcb db:createCollection users
tcb db:createCollection food_records
tcb db:createCollection weight_records
tcb db:createCollection sport_records
tcb db:createCollection food_library
```

### 3. 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核

## 🧮 核心算法

### 基础代谢率计算（BMR）
使用 Mifflin-St Jeor 公式：
- 男性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 + 5
- 女性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 - 161

### 每日总消耗（TDEE）
TDEE = BMR × 活动系数
- 久坐：1.2
- 轻度活动：1.375
- 中度活动：1.55
- 重度活动：1.725

### 减重热量计算
每周减重0.5kg需要每日热量缺口约500kcal
目标热量 = TDEE - (减重速度 × 500)

## 📱 使用说明

### 1. 首次使用
1. 完善个人档案信息
2. 设定减重目标
3. 开始记录饮食和运动

### 2. 日常使用
1. 每日记录饮食（推荐使用拍照识别）
2. 定期记录体重
3. 记录运动消耗
4. 查看数据报告和建议

### 3. 功能特色
- **AI识别**：拍照即可识别食物和营养成分
- **智能建议**：基于数据分析提供个性化建议
- **科学计算**：基于权威公式计算各项指标
- **趋势分析**：多维度数据分析和可视化

## 🔒 隐私保护

- 所有用户数据基于微信OpenID隔离
- 数据库权限设置为"仅创建者可读写"
- 敏感操作通过云函数处理
- 图片上传使用云存储临时链接

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 项目 Issues
- 邮箱：<EMAIL>

---

**智食派** - 让减重更科学，让健康更简单！ 🎯
