// 图标组件
Component({
  properties: {
    // 图标名称
    name: {
      type: String,
      value: ''
    },
    // 尺寸：xs, sm, md, lg, xl, xxl
    size: {
      type: String,
      value: 'md'
    },
    // 颜色：primary, secondary, success, warning, error, info
    color: {
      type: String,
      value: 'primary'
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    
  },

  methods: {
    
  }
})
