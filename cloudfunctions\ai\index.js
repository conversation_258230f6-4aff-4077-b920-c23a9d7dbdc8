// AI识别云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action } = event;

  try {
    switch (action) {
      case 'recognizeFood':
        return await recognizeFood(event.imageUrl);
      case 'getRecommendations':
        return await getRecommendations(event.foodData);
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { success: false, error: error.message };
  }
};

// 识别食物图片
async function recognizeFood(imageUrl) {
  // 这里应该调用第三方AI识别服务
  // 暂时返回模拟数据
  
  // 模拟AI识别结果
  const mockResults = [
    {
      name: '苹果',
      confidence: 0.95,
      amount: 150,
      unit: 'g',
      calories: 78,
      protein: 0.5,
      carbs: 20.7,
      fat: 0.3,
      fiber: 3.6,
      gi: 'low',
      satietyIndex: 4,
      dietFriendly: 5
    },
    {
      name: '香蕉',
      confidence: 0.88,
      amount: 120,
      unit: 'g',
      calories: 107,
      protein: 1.3,
      carbs: 27.4,
      fat: 0.4,
      fiber: 3.1,
      gi: 'medium',
      satietyIndex: 3,
      dietFriendly: 4
    }
  ];

  // 随机选择1-2个识别结果
  const numResults = Math.floor(Math.random() * 2) + 1;
  const selectedResults = mockResults.slice(0, numResults);

  return {
    success: true,
    data: {
      foods: selectedResults,
      confidence: selectedResults.reduce((sum, food) => sum + food.confidence, 0) / selectedResults.length
    }
  };
}

// 获取智能建议
async function getRecommendations(foodData) {
  if (!foodData || !foodData.foods) {
    return { success: true, data: [] };
  }

  const recommendations = [];
  
  // 分析食物并生成建议
  foodData.foods.forEach(food => {
    // 高热量食物建议
    if (food.calories > 200) {
      recommendations.push({
        title: '高热量提醒',
        description: `${food.name}热量较高，建议适量食用`,
        icon: '⚠️',
        type: 'warning'
      });
    }

    // 低GI食物推荐
    if (food.gi === 'low') {
      recommendations.push({
        title: '低GI食物',
        description: `${food.name}是低GI食物，有助于稳定血糖`,
        icon: '✅',
        type: 'positive'
      });
    }

    // 高纤维食物推荐
    if (food.fiber > 3) {
      recommendations.push({
        title: '高纤维食物',
        description: `${food.name}富含膳食纤维，增强饱腹感`,
        icon: '🌾',
        type: 'positive'
      });
    }

    // 减肥友好度建议
    if (food.dietFriendly >= 4) {
      recommendations.push({
        title: '减肥友好',
        description: `${food.name}是减肥友好食物，可以放心食用`,
        icon: '👍',
        type: 'positive'
      });
    }
  });

  // 去重并限制数量
  const uniqueRecommendations = recommendations
    .filter((rec, index, self) => 
      index === self.findIndex(r => r.title === rec.title)
    )
    .slice(0, 3);

  // 添加通用建议
  if (uniqueRecommendations.length < 3) {
    const generalRecommendations = [
      {
        title: '搭配建议',
        description: '建议搭配蔬菜一起食用，增加营养密度',
        icon: '🥗',
        type: 'suggestion'
      },
      {
        title: '饮水提醒',
        description: '餐前30分钟喝水有助于增强饱腹感',
        icon: '💧',
        type: 'suggestion'
      },
      {
        title: '细嚼慢咽',
        description: '慢慢咀嚼有助于消化和控制食量',
        icon: '⏰',
        type: 'suggestion'
      }
    ];

    const needed = 3 - uniqueRecommendations.length;
    uniqueRecommendations.push(...generalRecommendations.slice(0, needed));
  }

  return {
    success: true,
    data: uniqueRecommendations
  };
}

// 调用第三方AI识别服务（示例）
async function callAIService(imageUrl) {
  // 这里应该调用实际的AI服务
  // 例如：百度AI、腾讯AI、阿里云AI等
  
  /*
  const response = await cloud.callFunction({
    name: 'third-party-ai',
    data: {
      image: imageUrl,
      type: 'food_recognition'
    }
  });
  
  return response.result;
  */
  
  // 暂时返回模拟数据
  return null;
}
