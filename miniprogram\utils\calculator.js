// 智食派算法计算工具函数

/**
 * 计算基础代谢率 (BMR) - 使用 Mifflin-St Jeor 公式
 * @param {number} weight - 体重 (kg)
 * @param {number} height - 身高 (cm)
 * @param {number} age - 年龄
 * @param {string} gender - 性别 ('male' | 'female')
 * @returns {number} BMR值
 */
function calculateBMR(weight, height, age, gender) {
  if (gender === 'male') {
    return 10 * weight + 6.25 * height - 5 * age + 5;
  } else {
    return 10 * weight + 6.25 * height - 5 * age - 161;
  }
}

/**
 * 计算每日总消耗 (TDEE)
 * @param {number} bmr - 基础代谢率
 * @param {string} activityLevel - 活动水平
 * @returns {number} TDEE值
 */
function calculateTDEE(bmr, activityLevel) {
  const activityMultipliers = {
    sedentary: 1.2,    // 久坐
    light: 1.375,      // 轻度活动
    moderate: 1.55,    // 中度活动
    heavy: 1.725       // 重度活动
  };
  return Math.round(bmr * (activityMultipliers[activityLevel] || 1.2));
}

/**
 * 计算减肥热量目标
 * @param {number} tdee - 每日总消耗
 * @param {number} weightLossRate - 每周减重目标 (kg)
 * @returns {number} 每日热量目标
 */
function calculateWeightLossCalories(tdee, weightLossRate) {
  // 每周减重0.5kg需要每日热量缺口约500kcal
  const dailyDeficit = weightLossRate * 500;
  return Math.max(tdee - dailyDeficit, 1200); // 最低不低于1200kcal
}

/**
 * 计算BMI
 * @param {number} weight - 体重 (kg)
 * @param {number} height - 身高 (cm)
 * @returns {number} BMI值
 */
function calculateBMI(weight, height) {
  const heightInMeters = height / 100;
  return Math.round((weight / (heightInMeters * heightInMeters)) * 10) / 10;
}

/**
 * 获取BMI状态
 * @param {number} bmi - BMI值
 * @returns {object} BMI状态信息
 */
function getBMIStatus(bmi) {
  if (bmi < 18.5) {
    return { status: 'underweight', text: '偏瘦', color: '#3b82f6' };
  } else if (bmi < 24) {
    return { status: 'normal', text: '正常', color: '#10b981' };
  } else if (bmi < 28) {
    return { status: 'overweight', text: '超重', color: '#f59e0b' };
  } else {
    return { status: 'obese', text: '肥胖', color: '#ef4444' };
  }
}

/**
 * 理论减重计算
 * @param {number} calorieDeficit - 热量缺口 (kcal)
 * @returns {number} 预期减重 (kg)
 */
function predictWeightLoss(calorieDeficit) {
  // 7700kcal ≈ 1kg脂肪
  return Math.round((calorieDeficit / 7700) * 100) / 100;
}

/**
 * 预计达成时间计算
 * @param {number} currentWeight - 当前体重
 * @param {number} targetWeight - 目标体重
 * @param {number} weeklyWeightLoss - 每周减重速度
 * @returns {number} 预计周数
 */
function estimateTimeToGoal(currentWeight, targetWeight, weeklyWeightLoss) {
  const totalWeightLoss = currentWeight - targetWeight;
  return Math.ceil(totalWeightLoss / weeklyWeightLoss);
}

/**
 * 减重速度健康检查
 * @param {number} weeklyWeightLoss - 每周减重速度
 * @returns {object} 健康状态信息
 */
function checkWeightLossRate(weeklyWeightLoss) {
  if (weeklyWeightLoss > 1) {
    return { 
      status: 'warning', 
      message: '减重速度过快，建议降低热量缺口',
      color: '#f59e0b'
    };
  } else if (weeklyWeightLoss < 0.2) {
    return { 
      status: 'info', 
      message: '减重速度较慢，可适当增加热量缺口',
      color: '#3b82f6'
    };
  }
  return { 
    status: 'success', 
    message: '减重速度健康',
    color: '#10b981'
  };
}

/**
 * 热量摄入安全检查
 * @param {number} dailyCalories - 每日热量摄入
 * @param {number} bmr - 基础代谢率
 * @returns {object} 安全状态信息
 */
function checkCalorieIntake(dailyCalories, bmr) {
  if (dailyCalories < bmr * 0.8) {
    return { 
      status: 'danger', 
      message: '热量摄入过低，可能影响基础代谢',
      color: '#ef4444'
    };
  }
  return { 
    status: 'safe',
    message: '热量摄入安全',
    color: '#10b981'
  };
}

/**
 * 计算营养素占比
 * @param {number} protein - 蛋白质 (g)
 * @param {number} carbs - 碳水化合物 (g)
 * @param {number} fat - 脂肪 (g)
 * @returns {object} 营养素占比
 */
function calculateNutrientRatio(protein, carbs, fat) {
  const proteinCalories = protein * 4;
  const carbsCalories = carbs * 4;
  const fatCalories = fat * 9;
  const totalCalories = proteinCalories + carbsCalories + fatCalories;

  if (totalCalories === 0) {
    return { protein: 0, carbs: 0, fat: 0 };
  }

  return {
    protein: Math.round((proteinCalories / totalCalories) * 100),
    carbs: Math.round((carbsCalories / totalCalories) * 100),
    fat: Math.round((fatCalories / totalCalories) * 100)
  };
}

/**
 * 获取热量状态颜色
 * @param {number} consumed - 已摄入热量
 * @param {number} target - 目标热量
 * @returns {string} 状态颜色
 */
function getCalorieStatusColor(consumed, target) {
  const remaining = target - consumed;
  if (remaining > 200) {
    return '#10b981'; // 绿色：安全区
  } else if (remaining >= 0) {
    return '#f59e0b'; // 黄色：警告区
  } else {
    return '#ef4444'; // 红色：超标区
  }
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
 */
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

/**
 * 获取今日日期
 * @returns {string} 今日日期字符串
 */
function getTodayDate() {
  return formatDate(new Date());
}

/**
 * 计算两个日期之间的天数差
 * @param {string} date1 - 日期1 (YYYY-MM-DD)
 * @param {string} date2 - 日期2 (YYYY-MM-DD)
 * @returns {number} 天数差
 */
function getDaysDifference(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const timeDiff = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

module.exports = {
  calculateBMR,
  calculateTDEE,
  calculateWeightLossCalories,
  calculateBMI,
  getBMIStatus,
  predictWeightLoss,
  estimateTimeToGoal,
  checkWeightLossRate,
  checkCalorieIntake,
  calculateNutrientRatio,
  getCalorieStatusColor,
  formatDate,
  getTodayDate,
  getDaysDifference
};
