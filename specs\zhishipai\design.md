# 智食派微信小程序技术方案设计

## 1. 技术架构

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    微信小程序前端                              │
├─────────────────────────────────────────────────────────────┤
│                    微信云开发平台                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│   云函数服务     │    云数据库      │      云存储              │
│   - 用户管理     │   - 用户数据     │   - 图片资源            │
│   - 数据分析     │   - 饮食记录     │   - 静态资源            │
│   - AI识别       │   - 运动记录     │                        │
│   - 算法计算     │   - 食物库       │                        │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 1.2 技术栈选择

#### 前端技术栈
- **框架**：微信小程序原生框架
- **基础库版本**：latest（支持最新特性）
- **UI组件**：微信小程序原生组件 + 自定义组件
- **状态管理**：小程序原生数据绑定 + 全局数据管理
- **样式**：WXSS + 响应式设计

#### 后端技术栈
- **云平台**：腾讯云 CloudBase
- **云函数**：Node.js 18.15（推荐版本）
- **数据库**：CloudBase 云数据库（MongoDB）
- **存储**：CloudBase 云存储
- **AI服务**：第三方食物识别API（如百度AI、腾讯AI）

## 2. 项目结构设计

```
zhishipai/
├── miniprogram/                 # 小程序前端代码
│   ├── pages/                   # 页面目录
│   │   ├── index/              # 首页（智能仪表盘）
│   │   ├── food/               # 饮食记录
│   │   ├── camera/             # AI拍照识别
│   │   ├── report/             # 数据报告
│   │   ├── sport/              # 运动管理
│   │   └── profile/            # 个人中心
│   ├── components/             # 自定义组件
│   │   ├── chart/              # 图表组件
│   │   ├── food-item/          # 食物条目组件
│   │   └── progress-ring/      # 进度环组件
│   ├── utils/                  # 工具函数
│   │   ├── api.js              # API封装
│   │   ├── calculator.js       # 算法计算
│   │   └── constants.js        # 常量定义
│   ├── images/                 # 图片资源
│   ├── app.js                  # 小程序入口
│   ├── app.json               # 小程序配置
│   └── app.wxss               # 全局样式
├── cloudfunctions/             # 云函数目录
│   ├── user/                   # 用户管理云函数
│   ├── food/                   # 饮食相关云函数
│   ├── analysis/               # 数据分析云函数
│   └── ai/                     # AI识别云函数
├── project.config.json         # 项目配置
└── cloudbaserc.json           # 云开发配置
```

## 3. 数据库设计

### 3.1 数据集合设计

#### users 集合（用户信息）
```json
{
  "_id": "auto_generated",
  "_openid": "user_openid",
  "profile": {
    "height": 170,
    "weight": 68.5,
    "targetWeight": 60,
    "age": 28,
    "gender": "female",
    "activityLevel": "light"
  },
  "goals": {
    "weightLossRate": 0.5,
    "targetDate": "2024-12-31",
    "dailyCalories": 1500
  },
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

#### food_records 集合（饮食记录）
```json
{
  "_id": "auto_generated",
  "_openid": "user_openid",
  "date": "2024-01-01",
  "meal": "breakfast",
  "foods": [
    {
      "name": "燕麦粥",
      "amount": 200,
      "unit": "g",
      "calories": 180,
      "protein": 6.2,
      "carbs": 32.1,
      "fat": 2.8,
      "fiber": 3.5
    }
  ],
  "totalCalories": 180,
  "recordType": "camera",
  "createdAt": "2024-01-01T08:00:00.000Z"
}
```

#### weight_records 集合（体重记录）
```json
{
  "_id": "auto_generated",
  "_openid": "user_openid",
  "weight": 68.2,
  "date": "2024-01-01",
  "createdAt": "2024-01-01T07:00:00.000Z"
}
```

#### sport_records 集合（运动记录）
```json
{
  "_id": "auto_generated",
  "_openid": "user_openid",
  "sportType": "running",
  "duration": 30,
  "calories": 320,
  "date": "2024-01-01",
  "createdAt": "2024-01-01T18:00:00.000Z"
}
```

#### food_library 集合（食物库）
```json
{
  "_id": "auto_generated",
  "name": "苹果",
  "category": "水果",
  "calories": 52,
  "protein": 0.3,
  "carbs": 13.8,
  "fat": 0.2,
  "fiber": 2.4,
  "gi": "low",
  "satietyIndex": 4,
  "dietFriendly": 5
}
```

## 4. 云函数设计

### 4.1 用户管理云函数 (user)
```javascript
// 功能：用户信息管理
// 接口：
// - getUserProfile: 获取用户档案
// - updateUserProfile: 更新用户档案
// - calculateBMR: 计算基础代谢率
// - calculateTDEE: 计算每日总消耗
```

### 4.2 饮食管理云函数 (food)
```javascript
// 功能：饮食记录和分析
// 接口：
// - addFoodRecord: 添加饮食记录
// - getFoodRecords: 获取饮食记录
// - searchFood: 搜索食物
// - analyzeDailyNutrition: 分析每日营养
```

### 4.3 数据分析云函数 (analysis)
```javascript
// 功能：数据统计和趋势分析
// 接口：
// - getWeightTrend: 获取体重趋势
// - getCalorieBalance: 获取热量平衡分析
// - getHabitAnalysis: 获取习惯分析
// - predictWeightLoss: 预测减重进度
```

### 4.4 AI识别云函数 (ai)
```javascript
// 功能：食物AI识别
// 接口：
// - recognizeFood: 识别食物图片
// - getRecommendations: 获取智能建议
```

## 5. API接口设计

### 5.1 用户相关接口
- `GET /user/profile` - 获取用户档案
- `POST /user/profile` - 更新用户档案
- `POST /user/weight` - 记录体重

### 5.2 饮食相关接口
- `POST /food/record` - 添加饮食记录
- `GET /food/records` - 获取饮食记录
- `GET /food/search` - 搜索食物
- `POST /food/recognize` - AI识别食物

### 5.3 运动相关接口
- `POST /sport/record` - 添加运动记录
- `GET /sport/records` - 获取运动记录

### 5.4 分析相关接口
- `GET /analysis/weight-trend` - 体重趋势
- `GET /analysis/calorie-balance` - 热量平衡
- `GET /analysis/habit` - 习惯分析

## 6. 安全性设计

### 6.1 身份认证
- 使用微信小程序云开发天然免登录特性
- 通过 `wx-server-sdk` 获取用户 `OPENID`
- 基于 `OPENID` 进行用户数据隔离

### 6.2 数据权限
- 数据库权限设置为"仅创建者可读写"
- 敏感操作通过云函数处理
- 用户数据基于 `_openid` 字段隔离

### 6.3 数据加密
- 敏感数据在云函数中处理
- 图片上传使用云存储临时链接
- API调用使用HTTPS加密传输

## 7. 性能优化

### 7.1 前端优化
- 图片懒加载和压缩
- 分页加载历史数据
- 本地缓存常用数据
- 组件按需加载

### 7.2 后端优化
- 数据库索引优化
- 云函数冷启动优化
- 批量数据处理
- 缓存热点数据

## 8. 测试策略

### 8.1 单元测试
- 算法函数测试
- 工具函数测试
- 组件功能测试

### 8.2 集成测试
- 云函数接口测试
- 数据库操作测试
- 第三方服务集成测试

### 8.3 用户测试
- 真机测试
- 不同网络环境测试
- 边界情况测试

## 9. 部署方案

### 9.1 开发环境
- 使用云开发测试环境
- 本地开发调试
- 微信开发者工具预览

### 9.2 生产环境
- 云开发正式环境
- 域名配置和SSL证书
- 监控和日志配置

## 10. 扩展性考虑

### 10.1 功能扩展
- 社交功能模块
- 营养师咨询模块
- 商城购买模块

### 10.2 技术扩展
- 多端适配（H5、App）
- 数据导出功能
- 第三方设备接入
