// 智食派数据报告页面
const { analysisAPI, userAPI, showLoading, hideLoading, showError } = require('../../utils/api');
const { calculateBMI, getBMIStatus, checkWeightLossRate } = require('../../utils/calculator');
const { TIME_RANGES, TIME_RANGE_NAMES } = require('../../utils/constants');

Page({
  data: {
    // 时间范围
    currentTimeRange: TIME_RANGES.WEEK,
    timeRangeOptions: [
      { value: TIME_RANGES.WEEK, label: TIME_RANGE_NAMES[TIME_RANGES.WEEK] },
      { value: TIME_RANGES.MONTH, label: TIME_RANGE_NAMES[TIME_RANGES.MONTH] },
      { value: TIME_RANGES.QUARTER, label: TIME_RANGE_NAMES[TIME_RANGES.QUARTER] }
    ],
    
    // 体重趋势数据
    weightTrend: {
      data: [],
      currentWeight: 0,
      startWeight: 0,
      weightChange: 0,
      weeklyRate: 0,
      healthStatus: {}
    },
    
    // 热量平衡数据
    calorieBalance: {
      data: [],
      avgIntake: 0,
      avgBurn: 0,
      avgDeficit: 0,
      deficitStatus: 'normal'
    },
    
    // 习惯分析数据
    habitAnalysis: {
      recordFrequency: 0,
      highCalorieFoods: [],
      peakHours: [],
      weeklyPattern: []
    },
    
    // 减重预测
    prediction: {
      estimatedWeeks: 0,
      targetDate: '',
      currentProgress: 0,
      recommendations: []
    },
    
    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.loadReportData();
  },

  onShow() {
    this.loadReportData();
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadReportData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 加载报告数据
  async loadReportData() {
    try {
      showLoading('加载中...');

      const timeRange = this.data.currentTimeRange;

      // 并行加载所有数据
      const [weightTrend, calorieBalance, habitAnalysis, prediction] = await Promise.all([
        this.loadWeightTrend(timeRange).catch(() => this.getMockWeightTrend()),
        this.loadCalorieBalance(timeRange).catch(() => this.getMockCalorieBalance()),
        this.loadHabitAnalysis(timeRange).catch(() => this.getMockHabitAnalysis()),
        this.loadPrediction().catch(() => this.getMockPrediction())
      ]);

      this.setData({
        weightTrend,
        calorieBalance,
        habitAnalysis,
        prediction
      });

    } catch (error) {
      console.error('加载报告数据失败:', error);
      // 使用模拟数据
      this.setData({
        weightTrend: this.getMockWeightTrend(),
        calorieBalance: this.getMockCalorieBalance(),
        habitAnalysis: this.getMockHabitAnalysis(),
        prediction: this.getMockPrediction()
      });
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 加载体重趋势
  async loadWeightTrend(days) {
    try {
      const data = await analysisAPI.getWeightTrend(days);
      
      if (data && data.length > 0) {
        const currentWeight = data[data.length - 1].weight;
        const startWeight = data[0].weight;
        const weightChange = currentWeight - startWeight;
        const weeklyRate = Math.abs(weightChange / (days / 7));
        const healthStatus = checkWeightLossRate(weeklyRate);

        return {
          data,
          currentWeight,
          startWeight,
          weightChange,
          weeklyRate,
          healthStatus
        };
      }
      
      return {
        data: [],
        currentWeight: 0,
        startWeight: 0,
        weightChange: 0,
        weeklyRate: 0,
        healthStatus: { status: 'info', message: '暂无数据', color: '#64748b' }
      };
    } catch (error) {
      console.error('加载体重趋势失败:', error);
      return {
        data: [],
        currentWeight: 0,
        startWeight: 0,
        weightChange: 0,
        weeklyRate: 0,
        healthStatus: { status: 'error', message: '加载失败', color: '#ef4444' }
      };
    }
  },

  // 加载热量平衡
  async loadCalorieBalance(days) {
    try {
      const data = await analysisAPI.getCalorieBalance(days);
      
      if (data && data.length > 0) {
        const totalIntake = data.reduce((sum, item) => sum + (item.intake || 0), 0);
        const totalBurn = data.reduce((sum, item) => sum + (item.burn || 0), 0);
        const avgIntake = Math.round(totalIntake / data.length);
        const avgBurn = Math.round(totalBurn / data.length);
        const avgDeficit = avgBurn - avgIntake;
        
        let deficitStatus = 'normal';
        if (avgDeficit > 500) {
          deficitStatus = 'high';
        } else if (avgDeficit < 200) {
          deficitStatus = 'low';
        }

        return {
          data,
          avgIntake,
          avgBurn,
          avgDeficit,
          deficitStatus
        };
      }
      
      return {
        data: [],
        avgIntake: 0,
        avgBurn: 0,
        avgDeficit: 0,
        deficitStatus: 'normal'
      };
    } catch (error) {
      console.error('加载热量平衡失败:', error);
      return {
        data: [],
        avgIntake: 0,
        avgBurn: 0,
        avgDeficit: 0,
        deficitStatus: 'error'
      };
    }
  },

  // 加载习惯分析
  async loadHabitAnalysis(days) {
    try {
      const data = await analysisAPI.getHabitAnalysis(days);
      
      return {
        recordFrequency: data.recordFrequency || 0,
        highCalorieFoods: data.highCalorieFoods || [],
        peakHours: data.peakHours || [],
        weeklyPattern: data.weeklyPattern || []
      };
    } catch (error) {
      console.error('加载习惯分析失败:', error);
      return {
        recordFrequency: 0,
        highCalorieFoods: [],
        peakHours: [],
        weeklyPattern: []
      };
    }
  },

  // 加载减重预测
  async loadPrediction() {
    try {
      const data = await analysisAPI.predictWeightLoss();
      
      return {
        estimatedWeeks: data.estimatedWeeks || 0,
        targetDate: data.targetDate || '',
        currentProgress: data.currentProgress || 0,
        recommendations: data.recommendations || []
      };
    } catch (error) {
      console.error('加载减重预测失败:', error);
      return {
        estimatedWeeks: 0,
        targetDate: '',
        currentProgress: 0,
        recommendations: []
      };
    }
  },

  // 切换时间范围
  onTimeRangeChange(e) {
    const timeRange = parseInt(e.detail.value);
    this.setData({ currentTimeRange: timeRange });
    this.loadReportData();
  },

  // 查看体重详情
  onViewWeightDetail() {
    wx.navigateTo({
      url: '/pages/weight/weight'
    });
  },

  // 查看热量详情
  onViewCalorieDetail() {
    wx.navigateTo({
      url: '/pages/calorie/calorie'
    });
  },

  // 查看习惯详情
  onViewHabitDetail() {
    wx.navigateTo({
      url: '/pages/habit/habit'
    });
  },

  // 查看建议详情
  onViewRecommendation(e) {
    const { recommendation } = e.currentTarget.dataset;
    wx.showModal({
      title: '个性化建议',
      content: recommendation.content,
      showCancel: false
    });
  },

  // 分享报告
  onShareReport() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 模拟数据方法
  getMockWeightTrend() {
    return {
      data: [
        { date: '2024-01-01', weight: 62.5 },
        { date: '2024-01-08', weight: 61.8 },
        { date: '2024-01-15', weight: 61.2 },
        { date: '2024-01-22', weight: 60.5 }
      ],
      currentWeight: 60.5,
      startWeight: 62.5,
      weightChange: -2.0,
      weeklyRate: 0.5,
      healthStatus: { status: 'success', message: '减重速度健康', color: '#10b981' }
    };
  },

  getMockCalorieBalance() {
    return {
      data: [
        { date: '2024-01-20', intake: 1400, burn: 1800, deficit: 400 },
        { date: '2024-01-21', intake: 1350, burn: 1750, deficit: 400 },
        { date: '2024-01-22', intake: 1500, burn: 1900, deficit: 400 }
      ],
      avgIntake: 1417,
      avgBurn: 1817,
      avgDeficit: 400,
      deficitStatus: 'normal'
    };
  },

  getMockHabitAnalysis() {
    return {
      recordFrequency: 85,
      highCalorieFoods: [
        { name: '巧克力', count: 3 },
        { name: '薯片', count: 2 }
      ],
      peakHours: [12, 18, 20],
      weeklyPattern: []
    };
  },

  getMockPrediction() {
    return {
      estimatedWeeks: 8,
      targetDate: '2024-03-15',
      currentProgress: 40,
      recommendations: [
        { title: '保持热量缺口', content: '建议每日保持300-500kcal的热量缺口', icon: '🔥' }
      ]
    };
  }
});
