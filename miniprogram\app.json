{"pages": ["pages/index/index", "pages/food/food", "pages/camera/camera", "pages/result/result", "pages/report/report", "pages/sport/sport", "pages/profile/profile", "pages/icon-demo/icon-demo"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#10B981", "navigationBarTitleText": "智食派", "navigationBarTextStyle": "white", "backgroundColor": "#F9FAFB"}, "tabBar": {"color": "#6B7280", "selectedColor": "#10B981", "backgroundColor": "#FFFFFF", "borderStyle": "white", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/food/food", "text": "饮食"}, {"pagePath": "pages/report/report", "text": "报告"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "usingComponents": {"icon": "/components/icon/icon", "circular-progress": "/components/circular-progress/circular-progress"}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["chooseLocation"], "cloud": true, "style": "v2", "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}