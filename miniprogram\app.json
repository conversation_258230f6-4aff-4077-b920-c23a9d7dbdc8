{"pages": ["pages/index/index", "pages/food/food", "pages/camera/camera", "pages/result/result", "pages/report/report", "pages/sport/sport", "pages/profile/profile"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#10B981", "navigationBarTitleText": "智食派", "navigationBarTextStyle": "white", "backgroundColor": "#F9FAFB"}, "usingComponents": {"icon": "/components/icon/icon", "circular-progress": "/components/circular-progress/circular-progress"}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["chooseLocation"], "cloud": true, "style": "v2", "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}